<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    public function index()
    {
        return view('auth.login');
    }

    public function processLogin(Request $request)
    {
        $request->validate([
            'email' => 'required',
            'password' => 'required',
        ]);

        // Cari user berdasarkan email
        $user = User::where('email', $request->email)->first();

        // Jika user tidak ditemukan
        if (!$user) {
            return back()->withErrors([
                'email' => 'Email tidak terdaftar dalam sistem.',
            ])->onlyInput('email');
        }

        // Jika password tidak cocok
        if (!Hash::check($request->password, $user->password)) {
            return back()->withErrors([
                'email' => 'Password yang Anda masukkan salah.',
            ])->onlyInput('email');
        }

        // Check if user is teacher or admin
        if (!in_array($user->role, ['teacher', 'admin'])) {
            return back()->withErrors([
                'email' => '<PERSON><PERSON><PERSON> di<PERSON>. <PERSON>ya guru dan admin yang dapat login.',
            ])->onlyInput('email');
        }

        // Jika user ditemukan dan password cocok
        // Login manual
        Auth::login($user, $request->has('remember'));

        // Regenerate session
        $request->session()->regenerate();

        // Redirect ke halaman admin
        return redirect()->intended('/dashboard');
    }

    public function logout(Request $request)
    {
        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/auth/login');
    }
}
