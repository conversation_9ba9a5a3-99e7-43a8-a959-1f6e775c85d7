

<?php $__env->startSection('content'); ?>
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8"><PERSON><PERSON>an</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Ujian</a></li>
                            <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Bank Soal</a></li>
                            <li class="breadcrumb-item" aria-current="page">Tambah Bank Soal</li>
                        </ol>
                    </nav>
                </div>

                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form action="<?php echo e(route('question.add.post')); ?>" method="POST" autocomplete="off">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nama Bank Soal</label><span class="text-danger">*</span>
                                    <input type="text" class="form-control" id="name" name="name"
                                        placeholder="Masukkan Judul Bank Soal" required>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Deskripsi Bank Soal</label><span class="text-danger">*</span>
                                    <textarea name="description" id="description" class="form-control" placeholder="Masukkan Deskripsi Bank Soal" required></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center justify-content-end gap-3">
                            <a href="<?php echo e(route('question.index')); ?>" class="btn btn-danger"><i
                                    class="ti ti-arrow-left"></i>
                                Kembali</a>
                            <button class="btn btn-primary">Submit <i class="ti ti-send"></i></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('main', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\examhots\examhots-web\resources\views/admin/questionbank/question/add.blade.php ENDPATH**/ ?>