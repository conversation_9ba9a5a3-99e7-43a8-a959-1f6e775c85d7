<!doctype html>
<html lang="id">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bank Soal - ExamHots</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap"
        rel="stylesheet">
    <style>
        iconify-icon {
            display: inline-block;
            color: #455A9D;
        }
    </style>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'manrope': ['Manrope', 'sans-serif'],
                    },
                    colors: {
                        'primary-blue': '#455A9D',
                        'secondary-blue': '#6366F1',
                    }
                }
            }
        }
    </script>
    <!-- Iconify Script - Load in head for better performance -->
    <script src="https://cdn.jsdelivr.net/npm/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
</head>

<body class="font-manrope bg-gray-50">
    <!-- Header -->
    <header class="relative bg-cover bg-center bg-no-repeat px-6 py-4"
        style="background-image: url('./assets/img/bg-login.svg');">
        <!-- Background overlay for better readability -->
        <div class="absolute inset-0 bg-white/10"></div>

        <div class="relative flex items-center justify-between">
            <!-- Left side - Navigation buttons -->
            <div class="flex items-center space-x-2">
                <button class="bg-primary-blue text-white px-6 py-3 rounded-full flex items-center space-x-2 shadow-sm">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                        </path>
                    </svg>
                    <span class="font-medium">Bank Soal</span>
                </button>
                <button
                    class="bg-white/80 text-gray-700 px-6 py-3 rounded-full flex items-center space-x-2 hover:bg-white/90 transition-colors shadow-sm">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4m-6 0h6m-6 0V7a1 1 0 00-1 1v9a2 2 0 002 2h4a2 2 0 002-2V8a1 1 0 00-1-1V7">
                        </path>
                    </svg>
                    <span class="font-medium">Jadwal Ujian</span>
                </button>
            </div>

            <!-- Center - Logo -->
            <div class="flex items-center">
                <img src="./assets/img/logo-um.png" alt="Logo UM" class="w-12 h-12">
            </div>

            <!-- Right side - Notification and Profile -->
            <div class="flex items-center space-x-3">
                <!-- Notification Button -->
                <div class="relative">
                    <button
                        class="text-gray-700 hover:text-gray-900 p-2 rounded-lg hover:bg-white/20 transition-colors relative">
                        <iconify-icon icon="iconamoon:notification" width="24" height="24"></iconify-icon>
                        <!-- Notification Badge -->
                        <span
                            class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                    </button>
                </div>

                <!-- Profile Dropdown -->
                <div class="relative">
                    <button id="profileButton"
                        class="flex items-center space-x-2 text-gray-700 hover:text-gray-900 p-1 rounded-lg hover:bg-white/20 transition-colors">
                        <div class="w-10 h-10 bg-pink-200 rounded-full overflow-hidden border-2 border-white shadow-sm">
                            <img src="./assets/img/profile.jpg" alt="Profile" class="w-full h-full object-cover">
                        </div>
                        <iconify-icon icon="mdi:chevron-down" width="16" height="16" id="chevronIcon"></iconify-icon>
                    </button>

                    <!-- Dropdown Menu -->
                    <div id="profileDropdown"
                        class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 hidden">
                        <!-- User Info -->
                        <div class="px-4 py-3 border-b border-gray-100">
                            <div class="flex items-center space-x-3">
                                <div
                                    class="w-10 h-10 bg-pink-200 rounded-full overflow-hidden border-2 border-white shadow-sm flex-shrink-0">
                                    <img src="./assets/img/profile.jpg" alt="Profile"
                                        class="w-full h-full object-cover">
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900 truncate">Dr. Sarah Johnson</p>
                                    <p class="text-xs text-gray-500 truncate"><EMAIL></p>
                                </div>
                            </div>
                        </div>

                        <!-- Menu Items -->
                        <div class="py-1">
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                                <iconify-icon icon="mdi:account-circle" width="16" height="16"
                                    class="mr-3 text-gray-400"></iconify-icon>
                                Profil Saya
                            </a>
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                                <iconify-icon icon="mdi:cog" width="16" height="16"
                                    class="mr-3 text-gray-400"></iconify-icon>
                                Pengaturan
                            </a>
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                                <iconify-icon icon="mdi:help-circle" width="16" height="16"
                                    class="mr-3 text-gray-400"></iconify-icon>
                                Bantuan
                            </a>
                        </div>

                        <!-- Divider -->
                        <hr class="border-gray-100 my-1">

                        <!-- Logout -->
                        <div class="py-1">
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                                <iconify-icon icon="mdi:logout" width="16" height="16"
                                    class="mr-3 text-red-600"></iconify-icon>
                                Keluar
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container-fluid px-6 py-8">
        <!-- Page Header -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center space-x-3">
                <svg class="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path
                        d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z">
                    </path>
                </svg>
                <h1 class="text-3xl font-bold text-gray-800">Bank Soal</h1>
            </div>

            <div class="flex items-center space-x-4">
                <!-- Search Bar -->
                <div class="relative">
                    <input type="text" placeholder="Cari bank soal di sini..."
                        class="w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent">
                    <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>

                <!-- Add Button -->
                <button id="createBankSoalBtn"
                    class="bg-primary-blue text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-opacity-90 hover:shadow-lg hover:scale-105 transition-all duration-200 ease-in-out">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    <span>Buat Bank Soal Baru</span>
                </button>
            </div>
        </div>

        <!-- Cards Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Card 1 - UTS IPA Kelas 8 SMP -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">UTS IPA Kelas 8 SMP Semester Genap</h3>
                    <button class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                            </path>
                        </svg>
                    </button>
                </div>
                <p class="text-gray-600 text-sm mb-4">
                    Ulangan Tengah Semester untuk mata pelajaran IPA, mencakup materi sistem pernapasan dan peredaran
                    darah manusia.
                </p>
                <!-- Divider -->
                <hr class="border-gray-200 mb-4">
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">10 Soal PG</span>
                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">5 Soal Uraian Singkat</span>
                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">2 Soal Essay</span>
                </div>
            </div>

            <!-- Card 2 - UTS IPA Kelas 8 SMP (Duplicate) -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">UTS IPA Kelas 8 SMP Semester Genap</h3>
                    <button class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                            </path>
                        </svg>
                    </button>
                </div>
                <p class="text-gray-600 text-sm mb-4">
                    Ulangan Tengah Semester untuk mata pelajaran IPA, mencakup materi sistem pernapasan dan peredaran
                    darah manusia.
                </p>
                <!-- Divider -->
                <hr class="border-gray-200 mb-4">
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">10 Soal PG</span>
                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">5 Soal Uraian Singkat</span>
                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">2 Soal Essay</span>
                </div>
            </div>

            <!-- Card 3 - UTS IPA Kelas 8 SMP (Duplicate) -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">UTS IPA Kelas 8 SMP Semester Genap</h3>
                    <button class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                            </path>
                        </svg>
                    </button>
                </div>
                <p class="text-gray-600 text-sm mb-4">
                    Ulangan Tengah Semester untuk mata pelajaran IPA, mencakup materi sistem pernapasan dan peredaran
                    darah manusia.
                </p>
                <!-- Divider -->
                <hr class="border-gray-200 mb-4">
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">10 Soal PG</span>
                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">3 Soal Uraian singkat</span>
                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">2 Soal Essay</span>
                </div>
            </div>

            <!-- Card 4 - Ujian Harian Matematika -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Ujian Harian Matematika Bab Persamaan Linear</h3>
                    <button class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                            </path>
                        </svg>
                    </button>
                </div>
                <p class="text-gray-600 text-sm mb-4">
                    Soal latihan harian tentang konsep dasar dan penyelesaian persamaan linear satu variabel.
                </p>
                <!-- Divider -->
                <hr class="border-gray-200 mb-4">
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">10 Soal PG</span>
                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">5 Soal Uraian Singkat</span>
                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">2 Soal Essay</span>
                </div>
            </div>

            <!-- Card 5 - Ujian Harian Matematika (Duplicate) -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Ujian Harian Matematika Bab Persamaan Linear</h3>
                    <button class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                            </path>
                        </svg>
                    </button>
                </div>
                <p class="text-gray-600 text-sm mb-4">
                    Soal latihan harian tentang konsep dasar dan penyelesaian persamaan linear satu variabel.
                </p>
                <!-- Divider -->
                <hr class="border-gray-200 mb-4">
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">10 Soal PG</span>
                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">5 Soal Uraian Singkat</span>
                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">2 Soal Essay</span>
                </div>
            </div>

            <!-- Card 6 - Ujian Harian Matematika (Duplicate) -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">Ujian Harian Matematika Bab Persamaan Linear</h3>
                    <button class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                            </path>
                        </svg>
                    </button>
                </div>
                <p class="text-gray-600 text-sm mb-4">
                    Soal latihan harian tentang konsep dasar dan penyelesaian persamaan linear satu variabel.
                </p>
                <!-- Divider -->
                <hr class="border-gray-200 mb-4">
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">10 Soal PG</span>
                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">3 Soal Uraian singkat</span>
                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">2 Soal Essay</span>
                </div>
            </div>

            <!-- Card 7 - UAS Bahasa Indonesia -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">UAS Bahasa Indonesia Kelas 8 SMP Semester Genap</h3>
                    <button class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                            </path>
                        </svg>
                    </button>
                </div>
                <p class="text-gray-600 text-sm mb-4">
                    Evaluasi akhir semester yang menguji pemahaman siswa terhadap struktur dan isi teks eksplanasi serta
                    teks cerita pendek.
                </p>
                <!-- Divider -->
                <hr class="border-gray-200 mb-4">
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">10 Soal PG</span>
                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">5 Soal Uraian Singkat</span>
                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">2 Soal Essay</span>
                </div>
            </div>

            <!-- Card 8 - UAS Bahasa Indonesia (Duplicate) -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">UAS Bahasa Indonesia Kelas 8 SMP Semester Genap</h3>
                    <button class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                            </path>
                        </svg>
                    </button>
                </div>
                <p class="text-gray-600 text-sm mb-4">
                    Evaluasi akhir semester yang menguji pemahaman siswa terhadap struktur dan isi teks eksplanasi serta
                    teks cerita pendek.
                </p>
                <!-- Divider -->
                <hr class="border-gray-200 mb-4">
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">10 Soal PG</span>
                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">5 Soal Uraian Singkat</span>
                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">2 Soal Essay</span>
                </div>
            </div>

            <!-- Card 9 - UAS Bahasa Indonesia (Duplicate) -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">UAS Bahasa Indonesia Kelas 8 SMP Semester Genap</h3>
                    <button class="text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path
                                d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z">
                            </path>
                        </svg>
                    </button>
                </div>
                <p class="text-gray-600 text-sm mb-4">
                    Evaluasi akhir semester yang menguji pemahaman siswa terhadap struktur dan isi teks eksplanasi serta
                    teks cerita pendek.
                </p>
                <!-- Divider -->
                <hr class="border-gray-200 mb-4">
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">10 Soal PG</span>
                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">3 Soal Uraian singkat</span>
                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">2 Soal Essay</span>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Buat Bank Soal Baru -->
    <div id="createBankSoalModal"
        class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg mx-auto transform transition-all duration-300 scale-95 opacity-0"
            id="modalContent">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-100">
                <div class="flex items-center space-x-4">
                    <!-- Icon -->
                    <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-primary-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                    </div>
                    <!-- Title and Subtitle -->
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900">Buat Bank Soal Baru</h2>
                        <p class="text-sm text-gray-500 mt-1">Beri judul dan deskripsi singkat untuk memulainya</p>
                    </div>
                </div>
                <!-- Close Button -->
                <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="p-6 space-y-6">
                <!-- Judul Bank Soal -->
                <div>
                    <label for="judulBankSoal" class="block text-sm font-medium text-gray-700 mb-2">
                        Judul Bank Soal<span class="text-red-500 ml-1">*</span>
                    </label>
                    <input type="text" id="judulBankSoal" placeholder="e.g.UTS IPA Kelas 8 SMP Semester Genap"
                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200">
                </div>

                <!-- Deskripsi Bank Soal -->
                <div>
                    <label for="deskripsiBankSoal" class="block text-sm font-medium text-gray-700 mb-2">
                        Deskripsi Bank Soal
                    </label>
                    <textarea id="deskripsiBankSoal" rows="4"
                        placeholder="e.g. Ulangan Tengah Semester untuk mata pelajaran IPA, mencakup materi sistem pernapasan dan peredaran darah manusia."
                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent transition-all duration-200 resize-none"></textarea>
                    <div class="flex justify-between items-center mt-2">
                        <span class="text-sm text-gray-500">Masukkan deskripsi</span>
                        <span class="text-sm text-gray-400" id="charCount">0/200</span>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-100">
                <button id="cancelBtn"
                    class="px-6 py-2.5 text-gray-600 hover:text-gray-800 font-medium transition-colors">
                    Batal
                </button>
                <button id="saveBtn"
                    class="px-6 py-2.5 bg-primary-blue text-white rounded-xl hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg">
                    Simpan & Lanjutkan
                </button>
            </div>
        </div>
    </div>

    <script>
        // Profile Dropdown Toggle
        const profileButton = document.getElementById('profileButton');
        const profileDropdown = document.getElementById('profileDropdown');
        const chevronIcon = document.getElementById('chevronIcon');

        profileButton.addEventListener('click', function (e) {
            e.stopPropagation();

            // Toggle dropdown visibility
            profileDropdown.classList.toggle('hidden');

            // Rotate chevron icon
            if (profileDropdown.classList.contains('hidden')) {
                chevronIcon.style.transform = 'rotate(0deg)';
            } else {
                chevronIcon.style.transform = 'rotate(180deg)';
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function (e) {
            if (!profileButton.contains(e.target) && !profileDropdown.contains(e.target)) {
                profileDropdown.classList.add('hidden');
                chevronIcon.style.transform = 'rotate(0deg)';
            }
        });

        // Close dropdown when pressing Escape key
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape') {
                profileDropdown.classList.add('hidden');
                chevronIcon.style.transform = 'rotate(0deg)';
            }
        });

        // Add smooth transition for chevron rotation
        chevronIcon.style.transition = 'transform 0.2s ease-in-out';

        // Modal Buat Bank Soal Baru
        const createBankSoalBtn = document.getElementById('createBankSoalBtn');
        const createBankSoalModal = document.getElementById('createBankSoalModal');
        const modalContent = document.getElementById('modalContent');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const saveBtn = document.getElementById('saveBtn');
        const judulBankSoal = document.getElementById('judulBankSoal');
        const deskripsiBankSoal = document.getElementById('deskripsiBankSoal');
        const charCount = document.getElementById('charCount');

        // Open modal
        createBankSoalBtn.addEventListener('click', function () {
            createBankSoalModal.classList.remove('hidden');
            setTimeout(() => {
                modalContent.classList.remove('scale-95', 'opacity-0');
                modalContent.classList.add('scale-100', 'opacity-100');
            }, 10);
            // Focus on first input
            setTimeout(() => {
                judulBankSoal.focus();
            }, 300);
        });

        // Close modal function
        function closeModal() {
            modalContent.classList.remove('scale-100', 'opacity-100');
            modalContent.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                createBankSoalModal.classList.add('hidden');
                // Reset form
                judulBankSoal.value = '';
                deskripsiBankSoal.value = '';
                charCount.textContent = '0/200';
            }, 300);
        }

        // Close modal events
        closeModalBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);

        // Close modal when clicking outside
        createBankSoalModal.addEventListener('click', function (e) {
            if (e.target === createBankSoalModal) {
                closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape' && !createBankSoalModal.classList.contains('hidden')) {
                closeModal();
            }
        });

        // Character counter for description
        deskripsiBankSoal.addEventListener('input', function () {
            const currentLength = this.value.length;
            const maxLength = 200;
            charCount.textContent = `${currentLength}/${maxLength}`;

            // Prevent typing beyond limit
            if (currentLength > maxLength) {
                this.value = this.value.substring(0, maxLength);
                charCount.textContent = `${maxLength}/${maxLength}`;
            }

            // Change color when approaching limit
            if (currentLength > 180) {
                charCount.classList.add('text-red-500');
                charCount.classList.remove('text-gray-400');
            } else {
                charCount.classList.remove('text-red-500');
                charCount.classList.add('text-gray-400');
            }
        });

        // Save button functionality
        saveBtn.addEventListener('click', function () {
            const judul = judulBankSoal.value.trim();
            const deskripsi = deskripsiBankSoal.value.trim();

            if (!judul) {
                alert('Judul Bank Soal harus diisi!');
                judulBankSoal.focus();
                return;
            }

            // Here you would typically send data to server
            console.log('Judul:', judul);
            console.log('Deskripsi:', deskripsi);

            // Show success message (you can replace with actual navigation)
            alert('Bank Soal berhasil dibuat!');
            closeModal();
        });
    </script>
</body>

</html>