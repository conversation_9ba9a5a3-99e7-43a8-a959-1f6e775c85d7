<!DOCTYPE html>
<html lang="en">

<head>
    <!--  Title -->
    <title>{{ $title ?? 'Modernize' }}</title>
    <!--  Required Meta Tag -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="handheldfriendly" content="true" />
    <meta name="MobileOptimized" content="width" />
    <meta name="description" content="Mordenize" />
    <meta name="author" content="" />
    <meta name="keywords" content="Mordenize" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--  Favicon -->
    <link rel="shortcut icon" type="image/png" href="{{ asset('package/dist/images/logos/favicon.png') }} " />
    <!-- Owl Carousel  -->
    <link rel="stylesheet"
        href="{{ asset('package/dist/libs/owl.carousel/package/dist/assets/owl.carousel.min.css') }}">

    <link rel="stylesheet" href="{{ asset('package/dist/libs/dropzone/dist/min/dropzone.min.css') }}">
    <!-- Core Css -->
    <link id="themeColors" rel="stylesheet" href="{{ asset('package/dist/css/style.min.css') }}" />
</head>

<body>
    <div class="container">
        <div class="card bg-light-info shadow-none position-relative overflow-hidden mt-2">
            <div class="card-body px-4 py-3">
                <div class="row align-items-center">
                    <div class="col-9">
                        <h4 class="fw-semibold mb-8">Detail Bank Soal</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Ujian</a></li>
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Bank Soal</a>
                                </li>
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Detail Bank
                                        Soal</a></li>
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Pilih Tipe
                                        Soal</a></li>
                                <li class="breadcrumb-item" aria-current="page">Pilihan Ganda</li>
                            </ol>
                        </nav>
                    </div>

                    <div class="col-3">
                        <div class="text-center mb-n5">
                            <img src="{{ asset('package/dist/images/breadcrumb/ChatBc.png') }}" alt=""
                                class="img-fluid mb-n4">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">

                        {{-- Bagian Dropzone --}}
                        <h6 class="card-title mb-3">Upload Gambar Soal<span class="text-danger">*</span></h6>
                        <form action="{{ route('upload.image') }}" method="POST" class="mb-4 dropzone" id="myDropzone"
                            enctype="multipart/form-data">
                            @csrf
                        </form>
                        {{-- Form Utama --}}
                        <form action="{{ route('question.detail.add.type.post', ['id' => $question->id, 'type' => $type]) }}"
                            method="POST">
                            @csrf

                            {{-- Hidden input buat nyimpan nama file dari Dropzone --}}
                            <input type="hidden" name="uploaded_image" id="uploadedImage">

                            {{-- Tambahkan isian soal lainnya di sini --}}
                            <div class="mb-3">
                                <label class="form-label" for="question_text">Pertanyaan</label><span class="text-danger">*</span>
                                <textarea name="question" id="question_text" class="form-control" placeholder="Masukkan Pertanyaan"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="answer" class="form-label">Jawaban</label><span class="text-danger">*</span>
                                @for ($i = 1; $i <= 5; $i++)
                                @php
                                    $letter = chr(64 + $i);
                                @endphp
                                <div class="form-check mb-2">
                                  <input class="form-check-input" type="radio" name="correct" id="answer{{ $i }}" value="{{ $letter }}">
                                  <label class="form-check-label" for="answer{{ $i }}">
                                    {{ chr(64 + $i) }} <!-- A, B, C, D, E -->
                                  </label>
                              
                                  <textarea class="form-control mt-2" name="answer_{{ $i }}" rows="2" placeholder="Isi jawaban pilihan {{ $letter }}"></textarea>
                                </div>
                                @endfor
                            </div>

                            {{-- Tombol aksi --}}
                            <div class="d-flex align-items-center justify-content-end gap-2 mt-4">
                                <a href="{{ route('question.detail', $question->id) }}" class="btn btn-danger">
                                    <i class="ti ti-arrow-left"></i> Kembali
                                </a>
                                <button class="btn btn-primary">Simpan <i class="ti ti-send"></i></button>
                            </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--  Customizer -->
    <!--  Import Js Files -->
    <script src="{{ asset('package/dist/libs/jquery/dist/jquery.min.js') }}"></script>
    <script src="{{ asset('package/dist/libs/simplebar/dist/simplebar.min.js') }}"></script>
    <script src="{{ asset('package/dist/libs/bootstrap/dist/js/bootstrap.bundle.min.js') }}"></script>
    <!--  core files -->
    <script src="{{ asset('package/dist/js/app.min.js') }}"></script>
    <script src="{{ asset('package/dist/js/app.init.js') }}"></script>
    <script src="{{ asset('package/dist/js/app-style-switcher.js') }}"></script>
    <script src="{{ asset('package/dist/js/sidebarmenu.js') }}"></script>
    <script src="{{ asset('package/dist/js/custom.js') }}"></script>
    <!--  current page js files -->
    <script src="{{ asset('package/dist/libs/owl.carousel/dist/owl.carousel.min.js') }}"></script>
    <script src="{{ asset('package/dist/libs/apexcharts/dist/apexcharts.min.js') }}"></script>
    <script src="{{ asset('package/dist/js/dashboard.js') }}"></script>

    <script src="{{ asset('package/dist/libs/dropzone/dist/min/dropzone.min.js') }}"></script>
    
    <script>
        conts dz = new Dropzone('#myDropzone', {
            url: "{{ route('upload.image') }}",
            paramName: "file",
            maxFilesize: 2,
            acceptedFiles: "image/*",
            addRemoveLinks:true
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            success: function(file, response) {
                console.log('Upload sukses:', response);
                document.getElementById('uploadedImage').value = response.filename;
            }
        });
    </script>

    @yield('script')
</body>

</html>
