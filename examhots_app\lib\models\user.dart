class User {
  final int id;
  final String? nip;
  final String name;
  final String email;
  final String role;
  final String? gender;
  final String? address;
  final String? phonenumber;
  final String? createdAt;
  final String? updatedAt;

  User({
    required this.id,
    this.nip,
    required this.name,
    required this.email,
    required this.role,
    this.gender,
    this.address,
    this.phonenumber,
    this.createdAt,
    this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      nip: json['nip'],
      name: json['name'],
      email: json['email'],
      role: json['role'],
      gender: json['gender'],
      address: json['address'],
      phonenumber: json['phonenumber'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nip': nip,
      'name': name,
      'email': email,
      'role': role,
      'gender': gender,
      'address': address,
      'phonenumber': phonenumber,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  bool get isTeacher => role == 'teacher';
  bool get isAdmin => role == 'admin';
}
