<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\UsersController;
use App\Http\Controllers\TeachersController;
use App\Http\Controllers\ClassController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\StudentController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

// Auth
Route::get('auth/login', [AuthController::class, 'index']);
Route::post('auth/login', [AuthController::class, 'processLogin'])->name('auth.login.post');
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// Crud Users
Route::get('management/users', [UsersController::class, 'index'])->name('users.index');
Route::get('management/users/add', [UsersController::class, 'add'])->name('users.add');
Route::post('management/users/add', [UsersController::class, 'processAdd'])->name('users.add.post');
Route::get('management/edit/{id}', [UsersController::class, 'edit'])->name('users.edit');
Route::put('management/edit/{id}', [UsersController::class, 'processEdit'])->name('users.edit.post');
Route::delete('management/delete/{id}', [UsersController::class, 'delete'])->name('users.delete');

// Crud Teachers
Route::get('management/teachers', [TeachersController::class, 'index'])->name('teachers.index');
Route::get('management/teachers/add', [TeachersController::class, 'add'])->name('teachers.add');
Route::post('management/teachers/add', [TeachersController::class, 'processAdd'])->name('teachers.add.post');
Route::get('management/teachers/edit/{id}', [TeachersController::class, 'edit'])->name('teachers.edit');
Route::put('management/teachers/edit/{id}', [TeachersController::class, 'processEdit'])->name('teachers.edit.post');
Route::delete('management/teachers/delete/{id}', [TeachersController::class, 'delete'])->name('teachers.delete');

// Crud Class
Route::get('management/class', [ClassController::class, 'index'])->name('class.index');
Route::get('management/class/add', [ClassController::class, 'add'])->name('class.add');
Route::post('management/class/add', [ClassController::class, 'processAdd'])->name('class.add.post');
Route::get('management/class/edit/{id}', [ClassController::class, 'edit'])->name('class.edit');
Route::put('management/class/edit/{id}', [ClassController::class, 'processEdit'])->name('class.edit.post');
Route::delete('management/class/delete/{id}', [ClassController::class, 'delete'])->name('class.delete');

//Crud Student
Route::get('management/student', [StudentController::class, 'index'])->name('student.index');
Route::get('management/student/add', [StudentController::class, 'add'])->name('student.add');
Route::post('management/student/add', [StudentController::class, 'processAdd'])->name('student.add.post');
Route::get('management/student/edit/{id}', [StudentController::class, 'edit'])->name('student.edit');
Route::put('management/student/edit/{id}', [StudentController::class, 'processEdit'])->name('student.edit.post');
Route::delete('management/student/delete/{id}', [StudentController::class, 'delete'])->name('student.delete');

//Bank Soal
Route::get('questionbank/question', [QuestionController::class, 'index'])->name('question.index');
Route::get('questionbank/question/add', [QuestionController::class, 'add'])->name('question.add');
Route::post('questionbank/question/add', [QuestionController::class, 'processAdd'])->name('question.add.post');
Route::get('questionbank/question/edit/{id}', [QuestionController::class, 'edit'])->name('question.edit');
Route::put('questionbank/question/edit/{id}', [QuestionController::class, 'processEdit'])->name('question.edit.post');
Route::delete('questionbank/question/delete/{id}', [QuestionController::class, 'delete'])->name('question.delete');
Route::get('questionbank/question/detail/{id}', [QuestionController::class, 'detail'])->name('question.detail');
Route::get('questionbank/question/detail/{id}/add', [QuestionController::class, 'addDetail'])->name('question.detail.add');// show form
Route::post('questionbank/question/detail/{id}/choose', [QuestionController::class, 'chooseDetailType'])->name('question.detail.choose');
Route::get('questionbank/question/detail/{id}/add/{type}', [QuestionController::class, 'addDetailType'])->name('question.detail.add.type');
Route::post('questionbank/question/detail/{id}/add/{type}', [QuestionController::class, 'processAddDetailType'])->name('question.detail.add.type.post');
Route::get('questionbank/question/detail/{id}/edit', [QuestionController::class, 'editDetail'])->name('question.detail.edit');
Route::put('questionbank/question/detail/{id}/edit', [QuestionController::class, 'processEditDetail'])->name('question.detail.edit.post');
Route::delete('questionbank/question/detail/{id}/delete', [QuestionController::class, 'deleteDetail'])->name('question.detail.delete');
Route::post('/upload-image', [QuestionController::class, 'upload'])->name('upload.image');

// EDIT per tipe
// Route::get('questionbank/question/detail/{id}/edit/{type}', [QuestionController::class, 'editDetailByType'])->name('question.detail.edit.type');
// Route::put('questionbank/question/detail/{id}/edit/{type}', [QuestionController::class, 'processEditDetailByType'])->name('question.detail.edit.type.post');
