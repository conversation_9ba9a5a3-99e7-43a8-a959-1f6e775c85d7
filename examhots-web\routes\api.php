<?php

use App\Http\Controllers\Api\AuthApiController;
use App\Http\Controllers\Api\QuestionMaterialApiController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Test route
Route::get('/test', function () {
    return response()->json(['message' => 'API is working']);
});

// Authentication routes
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthApiController::class, 'login']);
    Route::post('/logout', [AuthApiController::class, 'logout'])->middleware('auth:sanctum');
    Route::get('/profile', [AuthApiController::class, 'profile'])->middleware('auth:sanctum');
});

// Question Material routes (protected)
Route::middleware('auth:sanctum')->group(function () {
    Route::prefix('question-materials')->group(function () {
        Route::get('/', [QuestionMaterialApiController::class, 'index']);
        Route::get('/{id}', [QuestionMaterialApiController::class, 'show']);
        Route::post('/', [QuestionMaterialApiController::class, 'store']);
        Route::put('/{id}', [QuestionMaterialApiController::class, 'update']);
        Route::delete('/{id}', [QuestionMaterialApiController::class, 'destroy']);
    });
});
