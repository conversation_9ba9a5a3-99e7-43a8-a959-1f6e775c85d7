class QuestionMaterial {
  final int id;
  final String name;
  final String description;
  final int pgCount;
  final int uraianCount;
  final int esaiCount;
  final int totalQuestions;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  QuestionMaterial({
    required this.id,
    required this.name,
    required this.description,
    this.pgCount = 0,
    this.uraianCount = 0,
    this.esaiCount = 0,
    this.totalQuestions = 0,
    this.createdAt,
    this.updatedAt,
  });

  factory QuestionMaterial.fromJson(Map<String, dynamic> json) {
    return QuestionMaterial(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      pgCount: json['pg_count'] ?? 0,
      uraianCount: json['uraian_count'] ?? 0,
      esaiCount: json['esai_count'] ?? 0,
      totalQuestions: json['total_questions'] ?? 0,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'pg_count': pgCount,
      'uraian_count': uraianCount,
      'esai_count': esaiCount,
      'total_questions': totalQuestions,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'QuestionMaterial{id: $id, name: $name, description: $description, pgCount: $pgCount, uraianCount: $uraianCount, esaiCount: $esaiCount, totalQuestions: $totalQuestions}';
  }
}
