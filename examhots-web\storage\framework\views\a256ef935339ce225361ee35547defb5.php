<!DOCTYPE html>
<html lang="en">

<head>
    <!--  Title -->
    <title><?php echo e($title ?? 'Modernize'); ?></title>
    <!--  Required Meta Tag -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="handheldfriendly" content="true" />
    <meta name="MobileOptimized" content="width" />
    <meta name="description" content="Mordenize" />
    <meta name="author" content="" />
    <meta name="keywords" content="Mordenize" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--  Favicon -->
    <link rel="shortcut icon" type="image/png" href="<?php echo e(asset('package/dist/images/logos/favicon.png')); ?> " />
    <!-- Owl Carousel  -->
    <link rel="stylesheet"
        href="<?php echo e(asset('package/dist/libs/owl.carousel/package/dist/assets/owl.carousel.min.css')); ?>">

    <!-- Core Css -->
    <link id="themeColors" rel="stylesheet" href="<?php echo e(asset('package/dist/css/style.min.css')); ?>" />
</head>

<body>
    <!-- Preloader -->
    <div class="preloader">
        <img src="<?php echo e(asset('package/dist/images/logos/favicon.png')); ?>" alt="loader" class="lds-ripple img-fluid" />
    </div>

    <!-- Body Wrapper -->
    <div class="page-wrapper" id="main-wrapper" data-theme="blue_theme" data-layout="vertical" data-sidebartype="full"
        data-sidebar-position="fixed" data-header-position="fixed">
        <!-- Sidebar Start -->
        <aside class="left-sidebar">
            <!-- Sidebar scroll-->
            <div>
                <div class="brand-logo d-flex align-items-center justify-content-between">
                    <a href="./index.html" class="text-nowrap logo-img">
                        <img src="<?php echo e(asset('package/dist/images/logos/dark-logo.svg')); ?>" class="dark-logo"
                            width="180" alt="" />
                        <img src="<?php echo e(asset('package/dist/images/logos/light-logo.svg')); ?>" class="light-logo"
                            width="180" alt="" />
                    </a>

                    <div class="close-btn d-lg-none d-block sidebartoggler cursor-pointer" id="sidebarCollapse">
                        <i class="ti ti-x fs-8"></i>
                    </div>
                </div>

                <!-- Sidebar navigation-->
                <nav class="sidebar-nav scroll-sidebar" data-simplebar>
                    <ul id="sidebarnav">
                        <!-- =================== -->
                        <!-- Home -->
                        <!-- =================== -->
                        <li class="nav-small-cap">
                            <i class="ti ti-dots nav-small-cap-icon fs-4"></i>
                            <span class="hide-menu">Home</span>
                        </li>

                        <li class="sidebar-item">
                            <a class="sidebar-link" href="<?php echo e(route('dashboard')); ?>" aria-expanded="false">
                                <span>
                                    <i class="ti ti-home"></i>
                                </span>
                                <span class="hide-menu">Dashboard</span>
                            </a>
                        </li>

                        <?php if(Auth::check() && Auth::user()->role == 'admin'): ?>
                            <!-- ============================= -->
                            <!-- Manajemen -->
                            <!-- ============================= -->
                            <li class="nav-small-cap">
                                <i class="ti ti-dots nav-small-cap-icon fs-4"></i>
                                <span class="hide-menu">Manajemen</span>
                            </li>

                            <!-- =================== -->
                            <!-- Dashboard -->
                            <!-- =================== -->
                            <li class="sidebar-item">
                                <a class="sidebar-link" href="<?php echo e(route('users.index')); ?>" aria-expanded="false">
                                    <span>
                                        <i class="ti ti-users"></i>
                                    </span>
                                    <span class="hide-menu">Users</span>
                                </a>
                            </li>

                            <li class="sidebar-item">
                                <a class="sidebar-link" href="<?php echo e(route('teachers.index')); ?>" aria-expanded="false">
                                    <span>
                                        <i class="ti ti-users"></i>
                                    </span>
                                    <span class="hide-menu">Guru</span>
                                </a>
                            </li>

                            <li class="sidebar-item">
                                <a class="sidebar-link" href="<?php echo e(route('class.index')); ?>" aria-expanded="false">
                                    <span>
                                        <i class="ti ti-list"></i>
                                    </span>
                                    <span class="hide-menu">Kelas</span>
                                </a>
                            </li>

                            <li class="sidebar-item">
                                <a class="sidebar-link" href="<?php echo e(route('student.index')); ?>" aria-expanded="false">
                                    <span>
                                        <i class="ti ti-users"></i>
                                    </span>
                                    <span class="hide-menu">Murid</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <li class="nav-small-cap">
                            <i class="ti ti-dots nav-small-cap-icon fs-4"></i>
                            <span class="hide-menu">Ujian</span>
                        </li>

                        <li class="sidebar-item">
                            <a class="sidebar-link" href="<?php echo e(route('question.index')); ?>" aria-expanded="false">
                                <span>
                                    <i class="ti ti-help-hexagon"></i>
                                </span>
                                <span class="hide-menu">Bank Soal</span>
                            </a>
                        </li>
                    </ul>
                </nav>
                <!-- End Sidebar navigation -->
            </div>
            <!-- End Sidebar scroll-->
        </aside>
        <!--  Sidebar End -->

        <!--  Main wrapper -->
        <div class="body-wrapper">
            <!--  Header Start -->
            <header class="app-header">
                <nav class="navbar navbar-expand-lg navbar-light">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link sidebartoggler nav-icon-hover ms-n3" id="headerCollapse"
                                href="javascript:void(0)">
                                <i class="ti ti-menu-2"></i>
                            </a>
                        </li>
                    </ul>

                    <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
                        <div class="d-flex align-items-center justify-content-between">
                            <ul class="navbar-nav flex-row ms-auto align-items-center justify-content-center">
                                <li class="nav-item dropdown">
                                    <a class="nav-link pe-0" href="javascript:void(0)" id="drop1"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                        <div class="d-flex align-items-center">
                                            <div class="user-profile-img">
                                                <img src="<?php echo e(asset('package/dist/images/profile/user-1.jpg')); ?>"
                                                    class="rounded-circle" width="35" height="35"
                                                    alt="" />
                                            </div>
                                        </div>
                                    </a>

                                    <div class="dropdown-menu content-dd dropdown-menu-end dropdown-menu-animate-up"
                                        aria-labelledby="drop1">
                                        <div class="profile-dropdown position-relative" data-simplebar>
                                            <div class="py-3 px-7 pb-0">
                                                <h5 class="mb-0 fs-5 fw-semibold">User Profile</h5>
                                            </div>

                                            <div class="d-flex align-items-center py-9 mx-7 border-bottom">
                                                <img src="<?php echo e(asset('package/dist/images/profile/user-1.jpg')); ?>"
                                                    class="rounded-circle" width="80" height="80"
                                                    alt="" />

                                                <div class="ms-3">
                                                    <h5 class="mb-1 fs-3"><?php echo e($authUser->name); ?></h5>
                                                    <h5 class="mb-1 fs-3"><?php echo e($authUser->role); ?></h5>
                                                    <p class="mb-0 d-flex text-dark align-items-center gap-2">
                                                        <i class="ti ti-mail fs-4"></i> <?php echo e($authUser->email); ?>

                                                    </p>
                                                </div>
                                            </div>

                                            <div class="message-body">
                                                <a href="./page-user-profile.html"
                                                    class="py-8 px-7 mt-8 d-flex align-items-center">
                                                    <span
                                                        class="d-flex align-items-center justify-content-center bg-light rounded-1 p-6">
                                                        <img src="<?php echo e(asset('package/dist/images/svgs/icon-account.svg')); ?>"
                                                            alt="" width="24" height="24">
                                                    </span>

                                                    <div class="w-75 d-inline-block v-middle ps-3">
                                                        <h6 class="mb-1 bg-hover-primary fw-semibold"> My Profile </h6>
                                                        <span class="d-block text-dark">Account Settings</span>
                                                    </div>
                                                </a>
                                            </div>

                                            <form action="<?php echo e(route('logout')); ?>" method="POST"
                                                class="d-grid py-4 px-7 pt-8">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" class="btn btn-outline-primary">Log
                                                    Out</button>
                                            </form>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </nav>
            </header>
            <!-- Header End -->

            <div class="container-fluid">
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>
    </div>

    <!--  Customizer -->
    <!--  Import Js Files -->
    <script src="<?php echo e(asset('package/dist/libs/jquery/dist/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/libs/simplebar/dist/simplebar.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/libs/bootstrap/dist/js/bootstrap.bundle.min.js')); ?>"></script>
    <!--  core files -->
    <script src="<?php echo e(asset('package/dist/js/app.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/app.init.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/app-style-switcher.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/sidebarmenu.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/custom.js')); ?>"></script>
    <!--  current page js files -->
    <script src="<?php echo e(asset('package/dist/libs/owl.carousel/dist/owl.carousel.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/libs/apexcharts/dist/apexcharts.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/dashboard.js')); ?>"></script>

    <?php echo $__env->yieldContent('script'); ?>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\examhots\examhots-web\resources\views/main.blade.php ENDPATH**/ ?>