

<?php $__env->startSection('content'); ?>
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Manajemen Murid</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Manajemen</a></li>
                            <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Murid</a></li>
                            <li class="breadcrumb-item" aria-current="page">Tambah Murid</li>
                        </ol>
                    </nav>
                </div>

                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form action="<?php echo e(route('student.add.post')); ?>" method="POST" autocomplete="off">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <div class="col-6">
                                <div class="mb-3">
                                    <label for="nisn" class="form-label">Nisn</label><span class="text-danger">*</span>
                                    <input type="text" class="form-control" id="nisn" name="nisn"
                                        placeholder="Masukkan Nisn" required>
                                    <?php $__errorArgs = ['nisn'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"> <?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="mb-3">
                                    <label for="nis" class="form-label">Nis</label><span class="text-danger">*</span>
                                    <input type="text" class="form-control" id="nis" name="nis"
                                        placeholder="Masukkan Nis" required>
                                    <?php $__errorArgs = ['nis'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"> <?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Nama</label><span class="text-danger">*</span>
                                    <input type="text" class="form-control" id="name" name="name"
                                        placeholder="Masukkan Nama" required>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"> <?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="mb-3">
                                    <label for="classid" class="form-label">Kelas</label><span class="text-danger">*</span>
                                    <select name="classid" id="classid" class="form-select" required>
                                        <option value="">- Pilih Kelas -</option>
                                        <?php $__currentLoopData = $classStudent; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($class->id); ?>"><?php echo e($class->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php $__errorArgs = ['classid'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </select>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="mb-3">
                                    <label for="parentname" class="form-label">Nama Orang Tua</label><span
                                        class="text-danger">*</span>
                                    <input type="text" class="form-control" id="parentname" name="parentname"
                                        placeholder="Masukkan Nama Orang Tua" required>
                                    <?php $__errorArgs = ['parentname'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"> <?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="mb-3">
                                    <label for="gender" class="form-label">Jenis Kelamin</label><span
                                        class="text-danger">*</span>
                                    <select name="gender" id="gender" class="form-select" required>
                                        <option value=""> - Pilih Jenis Kelamin - </option>
                                        <option value="L">Laki-laki</option>
                                        <option value="P">Perempuan</option>
                                        <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </select>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="mb-3">
                                    <label for="phonenumber" class="form-label">Nomor Whatsapp</label><span
                                        class="text-danger">*(Nomor yang terdaftar di Whatsapp)</span>
                                    <input type="text" name="phonenumber" id="phonenumber" class="form-control"
                                        placeholder="Masukkan Nomor Whatsapp" required>
                                    <?php $__errorArgs = ['phonenumber'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"> <?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-6">
                                <div class="mb-3">
                                    <label for="religion" class="form-label">Agama</label>
                                    <?php
                                        $religion = ['Islam', 'Kristen', 'Katolik', 'Hindu', 'Budha', 'Konghucu'];
                                    ?>
                                    <select name="religion" id="religion" class="form-select">
                                        <option value="">- Pilih Agama -</option>
                                        <?php $__currentLoopData = $religion; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($value); ?>"> <?php echo e($value); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="address" class="form-label">Alamat</label>
                                    <textarea name="address" id="address" placeholder="Masukkan Alamat" class="form-control"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex align-items-center justify-content-end gap-3">
                            <a href="<?php echo e(route('student.index')); ?>" class="btn btn-danger"><i
                                    class="ti ti-arrow-left"></i>
                                Kembali</a>
                            <button class="btn btn-primary">Submit <i class="ti ti-send"></i></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('main', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\examhots\examhots-web\resources\views/admin/management/student/add.blade.php ENDPATH**/ ?>