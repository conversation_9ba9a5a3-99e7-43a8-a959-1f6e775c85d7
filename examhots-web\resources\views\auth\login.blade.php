<!doctype html>
<html lang="id">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - ExamHots</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap"
        rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'manrope': ['Manrope', 'sans-serif'],
                    },
                    colors: {
                        'primary-blue': '#455A9D',
                        'secondary-blue': '#6366F1',
                    }
                }
            }
        }
    </script>
</head>

<body class="font-manrope min-h-screen relative">
    <!-- Background with blur -->
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style="background-image: url('{{ asset('assets/img/bg-login.svg') }}'); filter: blur(8px);"></div>

    <!-- Content overlay -->
    <div class="relative min-h-screen flex items-center justify-center p-4">
        <!-- Login Card -->
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md p-8">
            <!-- Logo -->
            <div class="text-center mb-6">
                <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <img src="{{ asset('assets/img/logo-um.png') }}" alt="Logo UM" class="w-16 h-16 object-contain">
                </div>
            </div>

            <!-- Welcome Text -->
            <div class="text-center mb-8">
                <h1 class="text-2xl font-bold mb-2" style="color: #31406F;">
                    Selamat Datang! 👋
                </h1>
                <p class="text-sm" style="color: #667085;">
                    Hai, Guru! Yuk, bikin soal & atur ujian jadi lebih mudah di sini!
                </p>
            </div>

            <!-- Tabs -->
            <div class="flex mb-6 rounded-lg p-1" style="background-color: #F9FAFB;">
                <button class="flex-1 py-2 px-4 font-medium rounded-md transition-all duration-200"
                    style="background-color: #fff; color: #455A9D;">
                    Login
                </button>
                <button
                    class="flex-1 py-2 px-4 text-gray-600 font-medium rounded-md hover:bg-gray-200 transition-all duration-200">
                    Daftar
                </button>
            </div>

            <!-- Error Messages -->
            @if ($errors->any())
                <div id="error-message"
                    class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg transition-all duration-300">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <svg class="h-5 w-5 text-red-400 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-sm text-red-700">
                                @foreach ($errors->all() as $error)
                                    {{ $error }}
                                @endforeach
                            </div>
                        </div>
                        <button type="button" onclick="hideErrorMessage()" class="text-red-400 hover:text-red-600">
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            @endif

            <!-- Login Form -->
            <form method="POST" action="{{ route('auth.login.post') }}" autocomplete="off" class="space-y-4">
                @csrf
                <!-- Email Field -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Email<span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5" style="color: #455A9D;" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207">
                                </path>
                            </svg>
                        </div>
                        <input type="email" name="email" id="email" placeholder="e.g <EMAIL>"
                            value="{{ old('email') }}"
                            class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent @error('email') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                            required>
                    </div>
                </div>

                <!-- Password Field -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Password<span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5" style="color: #455A9D;" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                                </path>
                            </svg>
                        </div>
                        <input type="password" name="password" id="password" placeholder="Type your password here"
                            class="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent @error('email') border-red-300 focus:ring-red-500 focus:border-red-500 @enderror"
                            required>
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="togglePassword()">
                                <svg id="eye-icon" class="h-5 w-5" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                    </path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Remember Me & Forgot Password -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input type="checkbox" id="remember" name="remember"
                            class="h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded">
                        <label for="remember" class="ml-2 block text-sm text-gray-700">
                            Ingat Saya
                        </label>
                    </div>
                    <a href="#" class="text-sm text-primary-blue hover:underline">
                        Lupa Kata Sandi ?
                    </a>
                </div>

                <!-- Login Button -->
                <button type="submit"
                    class="w-full bg-primary-blue text-white py-3 px-4 rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-primary-blue focus:ring-offset-2 transition duration-200 flex items-center justify-center"
                    style="background-color: #455A9D;" onmouseover="this.style.backgroundColor='#3A4A85'"
                    onmouseout="this.style.backgroundColor='#455A9D'">
                    Login
                    <svg class="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                        </path>
                    </svg>
                </button>
            </form>

            <!-- Footer -->
            <div class="mt-6 text-center text-xs text-gray-500">
                Dengan Login Anda menyetujui
                <a href="#" class="text-primary-blue hover:underline">Kebijakan Privasi</a>
                dan
                <a href="#" class="text-primary-blue hover:underline">Syarat dan Ketentuan Layanan</a>
            </div>
        </div>
    </div>

    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const eyeIcon = document.getElementById('eye-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                `;
            } else {
                passwordInput.type = 'password';
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                `;
            }
        }

        function hideErrorMessage() {
            const errorMessage = document.getElementById('error-message');
            if (errorMessage) {
                errorMessage.style.opacity = '0';
                errorMessage.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    errorMessage.style.display = 'none';
                }, 300);
            }
        }

        // Auto hide error message after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const errorMessage = document.getElementById('error-message');
            if (errorMessage) {
                setTimeout(() => {
                    hideErrorMessage();
                }, 5000);
            }
        });
    </script>
</body>

</html>
