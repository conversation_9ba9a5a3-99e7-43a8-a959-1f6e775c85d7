<table class="table table-hover table-striped">
    <thead>
        <tr>
            <th>No</th>
            <th>Soal</th>
            <th><PERSON><PERSON><PERSON></th>
            <th>Aksi</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($questions as $index => $q)
            <tr>
                <td>{{ $loop->iteration }}</td>
                <td>
                    @if (!empty($q->img))
                        @foreach (explode(',', $q->img) as $img)
                            <img src="{{ asset('storage/uploads/images/question/' . trim($img)) }}" alt="Gambar Soal"
                                class="img-fluid mb-2 rounded shadow-sm me-2" style="max-width: 100px;">
                        @endforeach
                    @endif
                    <div>
                        {{ $q->question }}
                    </div>
                </td>
                <td>
                    @foreach ($q->answers as $answer)
                        <span @if ($answer->is_correct) style="color: red; font-weight: bold;" @endif>
                            {{ $answer->answer }}
                        </span>
                    @endforeach
                </td>
                <td>
                    <div class="d-flex flex-wrap gap-2 mb-1">
                        <a href="{{ route('question.detail.edit.type', $q->id) }}" class="btn btn-sm btn-primary"><i
                                class="ti ti-edit"></i> Edit</a>
                        <form action="{{ route('question.detail.delete', $q->id) }}" method="POST"
                            style="display:inline;">
                            @csrf
                            @method('DELETE')
                            <button class="btn btn-sm btn-danger"><i class="ti ti-trash"></i> Hapus</button>
                        </form>
                    </div>
                </td>
            </tr>
        @endforeach
    </tbody>
</table>
