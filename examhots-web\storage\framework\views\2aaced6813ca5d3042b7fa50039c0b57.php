<!DOCTYPE html>
<html lang="en">

<head>
    <!--  Title -->
    <title><?php echo e($title ?? 'Modernize'); ?></title>
    <!--  Required Meta Tag -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="handheldfriendly" content="true" />
    <meta name="MobileOptimized" content="width" />
    <meta name="description" content="Mordenize" />
    <meta name="author" content="" />
    <meta name="keywords" content="Mordenize" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--  Favicon -->
    <link rel="shortcut icon" type="image/png" href="<?php echo e(asset('package/dist/images/logos/favicon.png')); ?> " />
    <!-- Owl Carousel  -->
    <link rel="stylesheet"
        href="<?php echo e(asset('package/dist/libs/owl.carousel/package/dist/assets/owl.carousel.min.css')); ?>">

    <link rel="stylesheet" href="<?php echo e(asset('package/dist/libs/dropzone/dist/min/dropzone.min.css')); ?>">
    <!-- Core Css -->
    <link id="themeColors" rel="stylesheet" href="<?php echo e(asset('package/dist/css/style.min.css')); ?>" />

    <style>
        .dropzone .dz-preview .dz-image {
            border-radius: 8px;
            width: 120px;
            height: 120px;
            overflow: hidden;
        }

        .dropzone .dz-preview .dz-image img {
            object-fit: cover;
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="card bg-light-info shadow-none position-relative overflow-hidden mt-2">
            <div class="card-body px-4 py-3">
                <div class="row align-items-center">
                    <div class="col-9">
                        <h4 class="fw-semibold mb-8">Detail Bank Soal</h4>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Ujian</a></li>
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Bank Soal</a>
                                </li>
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Detail Bank
                                        Soal</a></li>
                                <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Pilih Tipe
                                        Soal</a></li>
                                <li class="breadcrumb-item" aria-current="page">Pilihan Ganda</li>
                            </ol>
                        </nav>
                    </div>

                    <div class="col-3">
                        <div class="text-center mb-n5">
                            <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                                class="img-fluid mb-n4">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                    
                        
                        <form
                            action="<?php echo e($mode === 'edit'
                                ? route('question.detail.edit.post', ['questionid' => $question->id, 'type' => $type])
                                : route('question.detail.add.type.post', ['materialid' => $material->id, 'type' => $type])); ?>"
                            method="POST">
                            <?php echo csrf_field(); ?>
                            <?php if($mode === 'edit'): ?>
                                <?php echo method_field('PUT'); ?>
                            <?php endif; ?>

                            
                            <input type="hidden" name="uploaded_images" id="uploadedImages"
                                value="<?php echo e($mode === 'edit' ? $question->img : ''); ?>">


                            <div id="myDropzone" class="dropzone mb-3"></div>

                            
                            <div class="mb-3">
                                <label class="form-label" for="question_text">Pertanyaan</label><span
                                    class="text-danger">*</span>
                                <textarea name="question" id="question_text" class="form-control" placeholder="Masukkan Pertanyaan"><?php echo e(old('question', $question->question ?? '')); ?></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="answer" class="form-label">Jawaban</label><span
                                    class="text-danger">*</span>
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <?php
                                        $letter = chr(64 + $i);
                                    ?>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="radio" name="correct"
                                            id="answer<?php echo e($i); ?>" value="<?php echo e($letter); ?>"
                                            <?php if(old('correct', $question->answers[$i - 1]->is_correct ?? false)): ?> checked <?php endif; ?>>
                                        <label class="form-check-label" for="answer<?php echo e($i); ?>">
                                            <?php echo e(chr(64 + $i)); ?> <!-- A, B, C, D, E -->
                                        </label>

                                        <textarea class="form-control mt-2" name="answer_<?php echo e($i); ?>" rows="2"
                                            placeholder="Isi jawaban pilihan <?php echo e($letter); ?>"><?php echo e(old("answer_$i", $question->answers[$i - 1]->answer ?? '')); ?></textarea>
                                    </div>
                                <?php endfor; ?>
                            </div>

                            
                            <div class="d-flex align-items-center justify-content-end gap-2 mt-4">
                                <a href="<?php echo e(route('question.detail', $mode === 'edit' ? $question->questionmaterialid : $material->id)); ?>"
                                    class="btn btn-danger">
                                    <i class="ti ti-arrow-left"></i> Kembali
                                </a>
                                <button class="btn btn-primary">
                                    <?php echo e($mode === 'edit' ? 'Simpan Perubahan' : 'Simpan'); ?>

                                    <i class="ti ti-send"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--  Customizer -->
    <!--  Import Js Files -->
    <script src="<?php echo e(asset('package/dist/libs/jquery/dist/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/libs/simplebar/dist/simplebar.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/libs/bootstrap/dist/js/bootstrap.bundle.min.js')); ?>"></script>
    <!--  core files -->
    <script src="<?php echo e(asset('package/dist/js/app.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/app.init.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/app-style-switcher.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/sidebarmenu.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/custom.js')); ?>"></script>
    <!--  current page js files -->
    <script src="<?php echo e(asset('package/dist/libs/owl.carousel/dist/owl.carousel.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/libs/apexcharts/dist/apexcharts.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/dashboard.js')); ?>"></script>

    <script src="<?php echo e(asset('package/dist/libs/dropzone/dist/min/dropzone.min.js')); ?>"></script>

    <script>
        Dropzone.autoDiscover = false;

        const uploadedImages = document.getElementById('uploadedImages');
        const existingImages = uploadedImages.value ? uploadedImages.value.split(',') : [];

        const dz = new Dropzone('#myDropzone', {
            url: "<?php echo e(route('upload.image')); ?>",
            paramName: "file",
            acceptedFiles: "image/*",
            addRemoveLinks: true,
            headers: {
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            },
            init: function() {
                const thisDropzone = this;

                // Loop gambar lama (kalau ada)
                existingImages.forEach(function(filename) {
                    const mockFile = {
                        name: filename,
                        size: 123456,
                        type: 'image/jpeg'
                    };

                    // Penting! set juga file.uploadedName agar konsisten
                    mockFile.uploadedName = filename;

                    thisDropzone.emit("addedfile", mockFile);
                    thisDropzone.emit("thumbnail", mockFile, "/storage/uploads/images/question/" +
                        filename);
                    thisDropzone.emit("complete", mockFile);
                    thisDropzone.files.push(mockFile);
                });
            },
            success: function(file, response) {
                console.log('Upload sukses:', response);

                // simpan filename dari server ke objek Dropzone
                file.uploadedName = response.filename;

                // update hidden input
                const current = uploadedImages.value ? uploadedImages.value.split(',') : [];
                current.push(response.filename);
                uploadedImages.value = current.join(',');

                console.log('Updated hidden input:', uploadedImages.value);
            },
            removedfile: function(file) {
                file.previewElement.remove();

                const filename = file.uploadedName || file.name;
                console.log('Trying to remove:', filename);

                const current = uploadedImages.value ? uploadedImages.value.split(',') : [];
                const updated = current.filter(f => f !== filename);
                uploadedImages.value = updated.join(',');

                console.log('After removal:', uploadedImages.value);
            }
        });
    </script>

    <?php echo $__env->yieldContent('script'); ?>
</body>

</html>
<?php /**PATH C:\xampp\htdocs\examhots\examhots-web\resources\views/admin/questionbank/question/form/form_pilihan_ganda.blade.php ENDPATH**/ ?>