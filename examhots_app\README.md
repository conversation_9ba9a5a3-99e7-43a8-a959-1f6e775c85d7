# ExamHots App

Aplikasi Flutter untuk sistem ujian online yang dirancang khusus untuk guru dan siswa.

## Fitur

- **Halaman Login**: Interface yang user-friendly dengan desain modern
- **Autentikasi**: Sistem login dengan email dan password
- **Remember Me**: Opsi untuk mengingat login pengguna
- **Responsive Design**: <PERSON>pilan yang optimal di berbagai ukuran layar
- **Custom Assets**: Mendukung logo dan background image custom

## Tampilan Login

Halaman login menampilkan:
- Logo Universitas Negeri Semarang (dari assets/logo.png)
- Background image di bagian bawah (dari assets/login-bg.jpg)
- Pesan selamat datang yang ramah
- Form login dengan validasi
- Tab untuk Login dan Daftar
- Checkbox "Ingat Saya"
- Link "Lupa Kata Sandi"
- Tombol login dengan ikon panah
- Footer dengan kebijakan privasi

## Color Scheme

- **Background**: #F8F9FD (Light blue-gray)
- **Primary Text**: #455A9D (Dark blue)
- **Secondary Text**: #666666 (Gray)
- **Accent**: #455A9D (Dark blue untuk buttons dan links)

## Assets Required

Untuk tampilan yang optimal, tambahkan file berikut ke folder `assets/`:

1. **logo.png** - Logo Universitas Negeri Semarang
   - Format: PNG dengan background transparan
   - Ukuran: 200x200 pixels (recommended)

2. **login-bg.jpg** - Background image untuk halaman login
   - Format: JPG
   - Ukuran: 1920x1080 pixels atau lebih
   - Akan ditampilkan di bagian bawah halaman

*Note: Jika file asset tidak tersedia, aplikasi akan menampilkan fallback (icon default dan gradient background).*

## Teknologi

- **Flutter**: Framework UI untuk pengembangan aplikasi cross-platform
- **Dart**: Bahasa pemrograman yang digunakan
- **Material Design**: Sistem desain Google untuk UI yang konsisten

## Cara Menjalankan

1. Pastikan Flutter sudah terinstall
2. Clone repository ini
3. Tambahkan file assets (logo.png dan login-bg.jpg) ke folder `assets/`
4. Jalankan `flutter pub get` untuk menginstall dependencies
5. Jalankan `flutter run -d chrome` untuk web atau `flutter run` untuk mobile

## Struktur Project

```
lib/
  main.dart          # File utama aplikasi dengan halaman login
assets/              # Folder untuk asset gambar
  logo.png          # Logo UNNES (tambahkan manual)
  login-bg.jpg      # Background image (tambahkan manual)
  README.md         # Instruksi untuk assets
```

## Kontribusi

Aplikasi ini dikembangkan untuk memudahkan proses pembuatan soal dan pengaturan ujian bagi guru.
