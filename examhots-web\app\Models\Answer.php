<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Answer extends Model
{
    use HasFactory;

    protected $table = 'answer';

    protected $fillable = [
        'questionid',
        'answer',
        'is_correct',
        'score',
    ];

    public function answer()
    {
        return $this->hasMany(Answer::class, 'questionid');     
    }
}
