<!doctype html>
<html lang="id">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Detail Bank Soal - ExamHots</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap"
        rel="stylesheet">
    <style>
        iconify-icon {
            display: inline-block;
            color: #455A9D;
        }
    </style>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'manrope': ['Manrope', 'sans-serif'],
                    },
                    colors: {
                        'primary-blue': '#455A9D',
                        'secondary-blue': '#6366F1',
                    }
                }
            }
        }
    </script>
    <!-- Iconify Script - Load in head for better performance -->
    <script src="https://cdn.jsdelivr.net/npm/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
</head>

<body class="font-manrope bg-gray-50">
    <!-- Header -->
    <header class="relative bg-cover bg-center bg-no-repeat px-6 py-4"
        style="background-image: url('./assets/img/bg-login.svg');">
        <!-- Background overlay for better readability -->
        <div class="absolute inset-0 bg-white/10"></div>

        <div class="relative flex items-center justify-between">
            <!-- Left side - Navigation buttons -->
            <div class="flex items-center space-x-2">
                <button class="bg-primary-blue text-white px-6 py-3 rounded-full flex items-center space-x-2 shadow-sm">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                        </path>
                    </svg>
                    <span class="font-medium">Bank Soal</span>
                </button>
                <button
                    class="bg-white/80 text-gray-700 px-6 py-3 rounded-full flex items-center space-x-2 hover:bg-white/90 transition-colors shadow-sm">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4m-6 0h6m-6 0V7a1 1 0 00-1 1v9a2 2 0 002 2h4a2 2 0 002-2V8a1 1 0 00-1-1V7">
                        </path>
                    </svg>
                    <span class="font-medium">Jadwal Ujian</span>
                </button>
            </div>

            <!-- Center - Logo -->
            <div class="flex items-center">
                <img src="./assets/img/logo-um.png" alt="Logo UM" class="w-12 h-12">
            </div>

            <!-- Right side - Notification and Profile -->
            <div class="flex items-center space-x-3">
                <!-- Notification Button -->
                <div class="relative">
                    <button
                        class="text-gray-700 hover:text-gray-900 p-2 rounded-lg hover:bg-white/20 transition-colors relative">
                        <iconify-icon icon="iconamoon:notification" width="24" height="24"></iconify-icon>
                        <!-- Notification Badge -->
                        <span
                            class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                    </button>
                </div>

                <!-- Profile Dropdown -->
                <div class="relative">
                    <button id="profileButton"
                        class="flex items-center space-x-2 text-gray-700 hover:text-gray-900 p-1 rounded-lg hover:bg-white/20 transition-colors">
                        <div class="w-10 h-10 bg-pink-200 rounded-full overflow-hidden border-2 border-white shadow-sm">
                            <img src="./assets/img/profile.jpg" alt="Profile" class="w-full h-full object-cover">
                        </div>
                        <iconify-icon icon="mdi:chevron-down" width="16" height="16" id="chevronIcon"></iconify-icon>
                    </button>

                    <!-- Dropdown Menu -->
                    <div id="profileDropdown"
                        class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 hidden">
                        <!-- User Info -->
                        <div class="px-4 py-3 border-b border-gray-100">
                            <div class="flex items-center space-x-3">
                                <div
                                    class="w-10 h-10 bg-pink-200 rounded-full overflow-hidden border-2 border-white shadow-sm flex-shrink-0">
                                    <img src="./assets/img/profile.jpg" alt="Profile"
                                        class="w-full h-full object-cover">
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900 truncate">Dr. Sarah Johnson</p>
                                    <p class="text-xs text-gray-500 truncate"><EMAIL></p>
                                </div>
                            </div>
                        </div>

                        <!-- Menu Items -->
                        <div class="py-1">
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                                <iconify-icon icon="mdi:account-circle" width="16" height="16"
                                    class="mr-3 text-gray-400"></iconify-icon>
                                Profil Saya
                            </a>
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                                <iconify-icon icon="mdi:cog" width="16" height="16"
                                    class="mr-3 text-gray-400"></iconify-icon>
                                Pengaturan
                            </a>
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                                <iconify-icon icon="mdi:help-circle" width="16" height="16"
                                    class="mr-3 text-gray-400"></iconify-icon>
                                Bantuan
                            </a>
                        </div>

                        <!-- Divider -->
                        <hr class="border-gray-100 my-1">

                        <!-- Logout -->
                        <div class="py-1">
                            <a href="#"
                                class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors">
                                <iconify-icon icon="mdi:logout" width="16" height="16"
                                    class="mr-3 text-red-600"></iconify-icon>
                                Keluar
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container-fluid px-6 py-8">
        <!-- Page Header -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center space-x-4">
                <!-- Back Button -->
                <button onclick="history.back()" class="text-gray-600 hover:text-gray-800 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7">
                        </path>
                    </svg>
                </button>
                <h1 class="text-3xl font-bold text-gray-800">Detail Bank Soal</h1>
            </div>

            <div class="flex items-center space-x-4">
                <!-- Search Bar -->
                <div class="relative">
                    <input type="text" placeholder="Cari soal di sini..."
                        class="w-80 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent">
                    <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>

                <!-- Add Question Button -->
                <button id="addQuestionBtn"
                    class="bg-primary-blue text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-opacity-90 hover:shadow-lg hover:scale-105 transition-all duration-200 ease-in-out">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    <span>Tambahkan Soal Baru</span>
                </button>
            </div>
        </div>

        <!-- Bank Soal Info -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <h2 class="text-2xl font-semibold text-gray-900 mb-2">UTS IPA Kelas 8 SMP Semester Genap</h2>
                    <p class="text-gray-600">
                        Ulangan Tengah Semester untuk mata pelajaran IPA, mencakup materi sistem pernapasan dan
                        peredaran darah manusia.
                    </p>
                </div>
                <!-- Edit Button -->
                <button class="text-gray-400 hover:text-gray-600 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z">
                        </path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Question Type Tabs -->
        <div class="bg-white rounded-lg shadow-sm mb-8">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6">
                    <button id="tabPilihanGanda"
                        class="tab-button py-4 px-1 border-b-2 border-primary-blue text-primary-blue font-medium text-sm transition-colors"
                        data-tab="pilihan-ganda">
                        Pilihan Ganda
                        <span class="tab-badge ml-2 bg-primary-blue text-white text-xs px-2 py-1 rounded-full">8</span>
                    </button>
                    <button id="tabUraianSingkat"
                        class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm transition-colors"
                        data-tab="uraian-singkat">
                        Uraian Singkat
                        <span class="tab-badge ml-2 bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">0</span>
                    </button>
                    <button id="tabEssay"
                        class="tab-button py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm transition-colors"
                        data-tab="essay">
                        Essay
                        <span class="tab-badge ml-2 bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded-full">0</span>
                    </button>
                </nav>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Pilihan Ganda Content -->
            <div id="content-pilihan-ganda" class="tab-pane active">
                <div class="bg-white rounded-lg shadow-sm p-12 text-center">
                    <div class="max-w-md mx-auto">
                        <div class="mb-6">
                            <svg class="w-16 h-16 text-gray-300 mx-auto" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Soal Pilihan Ganda</h3>
                        <p class="text-gray-500 mb-6">
                            Belum ada soal pilihan ganda yang ditambahkan. <a href="#"
                                class="text-primary-blue hover:underline">Ayo buat soal pilihan ganda pertamamu
                                sekarang!</a>
                        </p>
                        <div class="flex justify-center space-x-3">
                            <button class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors">
                                Kembali
                            </button>
                            <button
                                class="px-6 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg">
                                Simpan
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Uraian Singkat Content -->
            <div id="content-uraian-singkat" class="tab-pane hidden">
                <div class="bg-white rounded-lg shadow-sm p-12 text-center">
                    <div class="max-w-md mx-auto">
                        <div class="mb-6">
                            <svg class="w-16 h-16 text-gray-300 mx-auto" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z">
                                </path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Soal Uraian Singkat</h3>
                        <p class="text-gray-500 mb-6">
                            Belum ada soal uraian singkat yang ditambahkan. <a href="#"
                                class="text-primary-blue hover:underline">Ayo buat soal uraian singkat pertamamu
                                sekarang!</a>
                        </p>
                        <div class="flex justify-center space-x-3">
                            <button class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors">
                                Kembali
                            </button>
                            <button
                                class="px-6 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg">
                                Simpan
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Essay Content -->
            <div id="content-essay" class="tab-pane hidden">
                <div class="bg-white rounded-lg shadow-sm p-12 text-center">
                    <div class="max-w-md mx-auto">
                        <div class="mb-6">
                            <svg class="w-16 h-16 text-gray-300 mx-auto" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                                </path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">Belum Ada Soal Essay</h3>
                        <p class="text-gray-500 mb-6">
                            Belum ada soal essay yang ditambahkan. <a href="#"
                                class="text-primary-blue hover:underline">Ayo buat soal essay pertamamu sekarang!</a>
                        </p>
                        <div class="flex justify-center space-x-3">
                            <button class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors">
                                Kembali
                            </button>
                            <button
                                class="px-6 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg">
                                Simpan
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Pilih Tipe Pertanyaan -->
    <div id="questionTypeModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md mx-4 transform transition-all duration-300 scale-95 opacity-0"
            id="questionTypeModalContent">
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-6 rounded-t-2xl relative">
                <div class="flex items-center space-x-4">
                    <!-- Question Mark Icon -->
                    <div class="w-12 h-12 bg-primary-blue rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path
                                d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-semibold text-gray-900">Pilih Tipe Pertanyaan</h3>
                        <p class="text-gray-600 text-sm mt-1">Tentukan format soal yang ingin kamu buat.</p>
                    </div>
                </div>
                <!-- Close Button -->
                <button id="closeQuestionTypeModal"
                    class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>

            <!-- Progress Bar -->
            <div class="px-6 py-2">
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-primary-blue h-2 rounded-full w-1/2"></div>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="px-6 py-6 space-y-4">
                <!-- Pilihan Ganda Option -->
                <label
                    class="flex items-center p-4 border-2 border-gray-200 rounded-xl hover:border-primary-blue hover:bg-blue-50 cursor-pointer transition-all duration-200 group">
                    <input type="radio" name="questionType" value="pilihan-ganda" class="hidden question-type-radio">
                    <div class="flex items-center space-x-4 flex-1">
                        <!-- Icon -->
                        <div
                            class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200 transition-colors">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 text-lg">Pilihan Ganda</h4>
                            <p class="text-gray-600 text-sm">Pertanyaan dengan beberapa opsi jawaban. Siswa memilih satu
                                jawaban yang paling tepat.</p>
                        </div>
                    </div>
                    <!-- Radio Button -->
                    <div
                        class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center group-hover:border-primary-blue transition-colors">
                        <div class="w-3 h-3 bg-primary-blue rounded-full hidden radio-dot"></div>
                    </div>
                </label>

                <!-- Uraian Singkat Option -->
                <label
                    class="flex items-center p-4 border-2 border-gray-200 rounded-xl hover:border-primary-blue hover:bg-blue-50 cursor-pointer transition-all duration-200 group">
                    <input type="radio" name="questionType" value="uraian-singkat" class="hidden question-type-radio">
                    <div class="flex items-center space-x-4 flex-1">
                        <!-- Icon -->
                        <div
                            class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center group-hover:bg-yellow-200 transition-colors">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z">
                                </path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 text-lg">Uraian Singkat</h4>
                            <p class="text-gray-600 text-sm">Pertanyaan yang membutuhkan jawaban singkat dan ringkas,
                                biasanya dalam satu kata.</p>
                        </div>
                    </div>
                    <!-- Radio Button -->
                    <div
                        class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center group-hover:border-primary-blue transition-colors">
                        <div class="w-3 h-3 bg-primary-blue rounded-full hidden radio-dot"></div>
                    </div>
                </label>

                <!-- Essay Option -->
                <label
                    class="flex items-center p-4 border-2 border-gray-200 rounded-xl hover:border-primary-blue hover:bg-blue-50 cursor-pointer transition-all duration-200 group">
                    <input type="radio" name="questionType" value="essay" class="hidden question-type-radio">
                    <div class="flex items-center space-x-4 flex-1">
                        <!-- Icon -->
                        <div
                            class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                                </path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 text-lg">Esai</h4>
                            <p class="text-gray-600 text-sm">Pertanyaan yang membutuhkan jawaban yang lebih panjang dan
                                rinci.</p>
                        </div>
                    </div>
                    <!-- Radio Button -->
                    <div
                        class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center group-hover:border-primary-blue transition-colors">
                        <div class="w-3 h-3 bg-primary-blue rounded-full hidden radio-dot"></div>
                    </div>
                </label>
            </div>

            <!-- Modal Footer -->
            <div class="px-6 py-6 bg-gray-50 rounded-b-2xl flex justify-end space-x-3">
                <button id="cancelQuestionType"
                    class="px-6 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors">
                    Batal
                </button>
                <button id="continueQuestionType"
                    class="px-6 py-2 bg-primary-blue text-white rounded-lg hover:bg-opacity-90 font-medium transition-all duration-200 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled>
                    Lanjut Buat Soal
                </button>
            </div>
        </div>
    </div>

    <script>
        // Profile Dropdown Toggle (same as index.html)
        const profileButton = document.getElementById('profileButton');
        const profileDropdown = document.getElementById('profileDropdown');
        const chevronIcon = document.getElementById('chevronIcon');

        profileButton.addEventListener('click', function (e) {
            e.stopPropagation();
            profileDropdown.classList.toggle('hidden');

            if (profileDropdown.classList.contains('hidden')) {
                chevronIcon.style.transform = 'rotate(0deg)';
            } else {
                chevronIcon.style.transform = 'rotate(180deg)';
            }
        });

        document.addEventListener('click', function (e) {
            if (!profileButton.contains(e.target) && !profileDropdown.contains(e.target)) {
                profileDropdown.classList.add('hidden');
                chevronIcon.style.transform = 'rotate(0deg)';
            }
        });

        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape') {
                profileDropdown.classList.add('hidden');
                chevronIcon.style.transform = 'rotate(0deg)';
            }
        });

        chevronIcon.style.transition = 'transform 0.2s ease-in-out';

        // Add Question Button functionality - Open Question Type Modal
        const addQuestionBtn = document.getElementById('addQuestionBtn');
        const questionTypeModal = document.getElementById('questionTypeModal');
        const questionTypeModalContent = document.getElementById('questionTypeModalContent');
        const closeQuestionTypeModal = document.getElementById('closeQuestionTypeModal');
        const cancelQuestionType = document.getElementById('cancelQuestionType');
        const continueQuestionType = document.getElementById('continueQuestionType');

        addQuestionBtn.addEventListener('click', function () {
            openQuestionTypeModal();
        });

        function openQuestionTypeModal() {
            questionTypeModal.classList.remove('hidden');
            setTimeout(() => {
                questionTypeModalContent.classList.remove('scale-95', 'opacity-0');
                questionTypeModalContent.classList.add('scale-100', 'opacity-100');
            }, 10);
        }

        function closeQuestionTypeModalFunc() {
            questionTypeModalContent.classList.remove('scale-100', 'opacity-100');
            questionTypeModalContent.classList.add('scale-95', 'opacity-0');
            setTimeout(() => {
                questionTypeModal.classList.add('hidden');
                // Reset form
                document.querySelectorAll('.question-type-radio').forEach(radio => {
                    radio.checked = false;
                });
                document.querySelectorAll('.radio-dot').forEach(dot => {
                    dot.classList.add('hidden');
                });
                document.querySelectorAll('label').forEach(label => {
                    label.classList.remove('border-primary-blue', 'bg-blue-50');
                    label.classList.add('border-gray-200');
                });
                continueQuestionType.disabled = true;
            }, 300);
        }

        // Close modal events
        closeQuestionTypeModal.addEventListener('click', closeQuestionTypeModalFunc);
        cancelQuestionType.addEventListener('click', closeQuestionTypeModalFunc);

        // Close modal when clicking outside
        questionTypeModal.addEventListener('click', function (e) {
            if (e.target === questionTypeModal) {
                closeQuestionTypeModalFunc();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape' && !questionTypeModal.classList.contains('hidden')) {
                closeQuestionTypeModalFunc();
            }
        });

        // Handle radio button selection
        const questionTypeRadios = document.querySelectorAll('.question-type-radio');
        questionTypeRadios.forEach(radio => {
            radio.addEventListener('change', function () {
                // Reset all labels
                document.querySelectorAll('label').forEach(label => {
                    label.classList.remove('border-primary-blue', 'bg-blue-50');
                    label.classList.add('border-gray-200');
                });

                // Hide all radio dots
                document.querySelectorAll('.radio-dot').forEach(dot => {
                    dot.classList.add('hidden');
                });

                // Highlight selected option
                if (this.checked) {
                    const parentLabel = this.closest('label');
                    parentLabel.classList.remove('border-gray-200');
                    parentLabel.classList.add('border-primary-blue', 'bg-blue-50');

                    // Show radio dot for selected option
                    const radioDot = parentLabel.querySelector('.radio-dot');
                    radioDot.classList.remove('hidden');

                    // Enable continue button
                    continueQuestionType.disabled = false;
                }
            });
        });

        // Handle continue button click
        continueQuestionType.addEventListener('click', function () {
            const selectedType = document.querySelector('.question-type-radio:checked');
            if (selectedType) {
                const questionType = selectedType.value;
                // Here you can redirect to different question creation pages based on type
                switch (questionType) {
                    case 'pilihan-ganda':
                        alert('Mengarahkan ke halaman buat soal pilihan ganda...');
                        // window.location.href = 'buat-soal-pilihan-ganda.html';
                        break;
                    case 'uraian-singkat':
                        alert('Mengarahkan ke halaman buat soal uraian singkat...');
                        // window.location.href = 'buat-soal-uraian-singkat.html';
                        break;
                    case 'essay':
                        alert('Mengarahkan ke halaman buat soal essay...');
                        // window.location.href = 'buat-soal-essay.html';
                        break;
                }
                closeQuestionTypeModalFunc();
            }
        });

        // Tab Switching Functionality
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanes = document.querySelectorAll('.tab-pane');

        function switchTab(targetTab) {
            // Remove active state from all tabs
            tabButtons.forEach(button => {
                button.classList.remove('border-primary-blue', 'text-primary-blue');
                button.classList.add('border-transparent', 'text-gray-500');

                // Reset badge colors
                const badge = button.querySelector('.tab-badge');
                badge.classList.remove('bg-primary-blue', 'text-white');
                badge.classList.add('bg-gray-200', 'text-gray-600');
            });

            // Hide all tab panes
            tabPanes.forEach(pane => {
                pane.classList.add('hidden');
                pane.classList.remove('active');
            });

            // Activate target tab
            const targetButton = document.querySelector(`[data-tab="${targetTab}"]`);
            const targetPane = document.getElementById(`content-${targetTab}`);

            if (targetButton && targetPane) {
                // Activate tab button
                targetButton.classList.remove('border-transparent', 'text-gray-500');
                targetButton.classList.add('border-primary-blue', 'text-primary-blue');

                // Activate badge
                const badge = targetButton.querySelector('.tab-badge');
                badge.classList.remove('bg-gray-200', 'text-gray-600');
                badge.classList.add('bg-primary-blue', 'text-white');

                // Show target pane
                targetPane.classList.remove('hidden');
                targetPane.classList.add('active');
            }
        }

        // Add click event listeners to tab buttons
        tabButtons.forEach(button => {
            button.addEventListener('click', function () {
                const targetTab = this.getAttribute('data-tab');
                switchTab(targetTab);
            });
        });

        // Initialize first tab as active
        switchTab('pilihan-ganda');
    </script>
</body>

</html>