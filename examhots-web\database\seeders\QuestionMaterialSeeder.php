<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\QuestionMaterial;
use App\Models\Question;

class QuestionMaterialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample question materials
        $materials = [
            [
                'name' => 'UTS IPA Kelas 8 SMP Semester Genap',
                'description' => 'Ulangan Tengah Semester untuk mata pelajaran IPA, mencakup materi sistem pernapasan dan peredaran darah manusia.',
            ],
            [
                'name' => 'Ujian Harian Matematika Bab Persamaan Linear',
                'description' => 'Soal latihan harian tentang konsep dasar dan penyelesaian persamaan linear satu variabel.',
            ],
            [
                'name' => 'UAS Bahasa Indonesia Kelas 8 SMP Semester Genap',
                'description' => 'Evaluasi akhir semester yang menguji pemahaman siswa terhadap struktur dan isi teks eksplanasi serta teks cerita pendek.',
            ],
        ];

        foreach ($materials as $materialData) {
            $material = QuestionMaterial::create($materialData);

            // Create sample questions for each material
            $this->createSampleQuestions($material->id);
        }
    }

    private function createSampleQuestions($materialId)
    {
        // Create sample pilihan ganda questions
        for ($i = 1; $i <= 10; $i++) {
            Question::create([
                'questionmaterialid' => $materialId,
                'question' => "Contoh soal pilihan ganda nomor $i",
                'type' => 'pilihan_ganda',
            ]);
        }

        // Create sample uraian singkat questions
        for ($i = 1; $i <= 3; $i++) {
            Question::create([
                'questionmaterialid' => $materialId,
                'question' => "Contoh soal uraian singkat nomor $i",
                'type' => 'uraian_singkat',
            ]);
        }

        // Create sample esai questions
        for ($i = 1; $i <= 2; $i++) {
            Question::create([
                'questionmaterialid' => $materialId,
                'question' => "Contoh soal esai nomor $i",
                'type' => 'esai',
            ]);
        }
    }
}
