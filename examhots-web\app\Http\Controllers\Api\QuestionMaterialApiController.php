<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\QuestionMaterial;
use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class QuestionMaterialApiController extends Controller
{
    /**
     * Get all question materials with question counts
     */
    public function index()
    {
        try {
            // Get all question materials
            $questionMaterials = QuestionMaterial::all();
            
            // Add question counts for each material
            $questionMaterials = $questionMaterials->map(function ($material) {
                $questions = Question::where('questionmaterialid', $material->id)->get();
                
                $material->pg_count = $questions->where('type', 'pilihan_ganda')->count();
                $material->uraian_count = $questions->where('type', 'uraian_singkat')->count();
                $material->esai_count = $questions->where('type', 'esai')->count();
                $material->total_questions = $questions->count();
                
                return $material;
            });

            return response()->json([
                'success' => true,
                'message' => 'Question materials retrieved successfully',
                'data' => $questionMaterials
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve question materials',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific question material with details
     */
    public function show($id)
    {
        try {
            $questionMaterial = QuestionMaterial::find($id);
            
            if (!$questionMaterial) {
                return response()->json([
                    'success' => false,
                    'message' => 'Question material not found'
                ], 404);
            }

            $questions = Question::with('answers')
                ->where('questionmaterialid', $id)
                ->get();

            $questionMaterial->pg_count = $questions->where('type', 'pilihan_ganda')->count();
            $questionMaterial->uraian_count = $questions->where('type', 'uraian_singkat')->count();
            $questionMaterial->esai_count = $questions->where('type', 'esai')->count();
            $questionMaterial->total_questions = $questions->count();
            $questionMaterial->questions = $questions;

            return response()->json([
                'success' => true,
                'message' => 'Question material retrieved successfully',
                'data' => $questionMaterial
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve question material',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new question material
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'required|string',
            ]);

            $questionMaterial = QuestionMaterial::create([
                'name' => $request->name,
                'description' => $request->description,
            ]);

            // Add default counts
            $questionMaterial->pg_count = 0;
            $questionMaterial->uraian_count = 0;
            $questionMaterial->esai_count = 0;
            $questionMaterial->total_questions = 0;

            return response()->json([
                'success' => true,
                'message' => 'Question material created successfully',
                'data' => $questionMaterial
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create question material',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update question material
     */
    public function update(Request $request, $id)
    {
        try {
            $questionMaterial = QuestionMaterial::find($id);
            
            if (!$questionMaterial) {
                return response()->json([
                    'success' => false,
                    'message' => 'Question material not found'
                ], 404);
            }

            $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'required|string',
            ]);

            $questionMaterial->update([
                'name' => $request->name,
                'description' => $request->description,
            ]);

            // Add question counts
            $questions = Question::where('questionmaterialid', $id)->get();
            $questionMaterial->pg_count = $questions->where('type', 'pilihan_ganda')->count();
            $questionMaterial->uraian_count = $questions->where('type', 'uraian_singkat')->count();
            $questionMaterial->esai_count = $questions->where('type', 'esai')->count();
            $questionMaterial->total_questions = $questions->count();

            return response()->json([
                'success' => true,
                'message' => 'Question material updated successfully',
                'data' => $questionMaterial
            ], 200);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update question material',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete question material
     */
    public function destroy($id)
    {
        try {
            $questionMaterial = QuestionMaterial::find($id);
            
            if (!$questionMaterial) {
                return response()->json([
                    'success' => false,
                    'message' => 'Question material not found'
                ], 404);
            }

            // Delete related questions first
            Question::where('questionmaterialid', $id)->delete();
            
            // Delete the question material
            $questionMaterial->delete();

            return response()->json([
                'success' => true,
                'message' => 'Question material deleted successfully'
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete question material',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
