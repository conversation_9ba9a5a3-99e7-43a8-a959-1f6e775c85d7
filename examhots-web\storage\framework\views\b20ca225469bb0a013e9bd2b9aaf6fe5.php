<table class="table table-hover table-striped">
    <thead>
        <tr>
            <th>No</th>
            <th>Soal</th>
            <th><PERSON><PERSON><PERSON></th>
            <th>Aksi</th>
        </tr>
    </thead>
    <tbody>
        <?php $__currentLoopData = $questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $q): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td><?php echo e($loop->iteration); ?></td>
                <td>
                    <?php if(!empty($q->img)): ?>
                        <?php $__currentLoopData = explode(',', $q->img); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $img): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <img src="<?php echo e(asset('storage/uploads/images/question/' . trim($img))); ?>" alt="Gambar Soal"
                                class="img-fluid mb-2 rounded shadow-sm me-2" style="max-width: 100px;">
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                    <div>
                        <?php echo e($q->question); ?>

                    </div>
                </td>
                <td>
                    <?php
                        $labels = ['A', 'B', 'C', 'D', 'E'];
                    ?>
                    <ul class="list-unstyled mb-0">
                        <?php $__currentLoopData = $q->answers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $answer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <strong><?php echo e($labels[$key]); ?>.</strong>
                                <span <?php if($answer->is_correct): ?> style="color: red; font-weight: bold;" <?php endif; ?>>
                                    <?php echo e($answer->answer); ?>

                                </span>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </td>
                <td>
                    <div class="d-flex flex-wrap gap-2 mb-1">
                        <a href="<?php echo e(route('question.detail.edit.type', $q->id)); ?>" class="btn btn-sm btn-primary"><i
                                class="ti ti-edit"></i> Edit</a>
                        <form action="<?php echo e(route('question.detail.delete', $q->id)); ?>" method="POST"
                            style="display:inline;">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button class="btn btn-sm btn-danger"><i class="ti ti-trash"></i> Hapus</button>
                        </form>
                    </div>
                </td>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tbody>
</table>
<?php /**PATH C:\xampp\htdocs\examhots\examhots-web\resources\views/admin/questionbank/question/components/pilihan_ganda.blade.php ENDPATH**/ ?>