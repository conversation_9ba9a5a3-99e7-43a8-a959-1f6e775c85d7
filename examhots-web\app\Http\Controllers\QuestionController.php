<?php

namespace App\Http\Controllers;

use App\Models\Answer;
use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\QuestionMaterial;
use Illuminate\Support\Facades\Storage;

class QuestionController extends Controller
{
    public function index()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = QuestionMaterial::all();

        return view('admin.questionbank.question.index', [
            'title' => 'Bank Soal',
            'question' => $question,
        ]);
    }

    public function add()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        return view('admin.questionbank.question.add', [
            'title' => 'Tambah Bank Soal',
        ]);
    }

    public function processAdd(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $request->validate([
            'name' => 'required',
            'description' => 'required',
        ]);

        QuestionMaterial::create([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        return redirect()->route('question.index')->with('success', 'Data bank soal berhasil ditambahkan');
    }

    public function edit($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = QuestionMaterial::find($id);

        return view('admin.questionbank.question.edit', [
            'title' => 'Edit Bank Soal',
            'question' => $question,
        ]);
    }

    public function processEdit(Request $request, $id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $request->validate([
            'name' => 'required',
            'description' => 'required',
        ]);

        QuestionMaterial::where('id', $id)->update([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        return redirect()->route('question.index')->with('success', 'Data bank soal berhasil diubah');
    }

    public function delete($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = QuestionMaterial::find($id);

        $question->delete();

        return redirect()->route('question.index')->with('success', 'Data bank soal berhasil dihapus');
    }

    public function detail($materialid)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $questionmaterial = QuestionMaterial::find($materialid);

        $questions = Question::with('answers')
            ->where('questionmaterialid', $materialid)
            ->get();

        return view('admin.questionbank.question.detail', [
            'title' => 'Detail Bank Soal',
            'question' => $questionmaterial,
            'questions' => $questions,
            'pg_questions' => $questions->where('type', 'pilihan_ganda'),
            'urai_questions' => $questions->where('type', 'uraian_singkat'),
            'esai_questions' => $questions->where('type', 'esai'),
        ]);
    }

    public function addDetail(Request $request, $materialid)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = QuestionMaterial::findOrFail($materialid);

        return view('admin.questionbank.question.form.choose', compact('question') + [
            'title' => 'Form Pilih Soal',
        ]);
    }

    public function chooseDetailType(Request $request, $materialid)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $request->validate([
            'type' => 'required|in:pilihan_ganda,uraian_singkat,esai',
        ]);

        $type = $request->input('type');

        return redirect()->route('question.detail.add.type', [
            'materialid' => $materialid,
            'type' => $type,
        ]);
    }

    public function addDetailType($materialid, $type)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $allowedTypes = ['pilihan_ganda', 'uraian_singkat', 'esai'];
        if (!in_array($type, $allowedTypes)) {
            abort(404, 'Tipe soal tidak dikenali');
        }

        $question = QuestionMaterial::findOrFail($materialid);

        $view = match ($type) {
            'pilihan_ganda' => 'admin.questionbank.question.form.form_pilihan_ganda',
            'uraian_singkat' => 'admin.questionbank.question.form.form_uraian_singkat',
            'esai' => 'admin.questionbank.question.form.form_esai',
        };

        return view($view, [
            'title' => 'Buat Soal ' . ucwords(str_replace('_', ' ', $type)),
            'material' => $question,
            'type' => $type,
            'mode' => 'create',
        ]);
    }

    public function processAddDetailType(Request $request, $materialid, $type)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        if ($type === 'pilihan_ganda') {
            $request->validate([
                'question' => 'required|string',
                'answer_1' => 'required|string',
                'answer_2' => 'required|string',
                'answer_3' => 'required|string',
                'answer_4' => 'required|string',
                'answer_5' => 'required|string',
                'correct' => 'required|in:A,B,C,D,E',
            ]);
        } else if ($type === 'uraian_singkat') {
            $request->validate([
                'question' => 'required|string',
                'answer' => 'required|string',
            ]);
        } else if ($type === 'esai') {
            $request->validate([
                'question' => 'required|string',
                'answer' => 'required|string',
                'score' => 'required|numeric',
            ]);
        } else {
            abort(400, 'Tipe soal tidak dikenali.');
        }

        $question = new Question();
        $question->questionmaterialid = $materialid;
        $question->question = $request->input('question');
        $question->type = $type;
        $question->img = $request->input('uploaded_images');
        $question->save();


        if ($type === 'pilihan_ganda') {
            $opsi = [
                'A' => $request->input('answer_1'),
                'B' => $request->input('answer_2'),
                'C' => $request->input('answer_3'),
                'D' => $request->input('answer_4'),
                'E' => $request->input('answer_5'),
            ];

            foreach ($opsi as $label => $jawaban) {
                if (trim($jawaban) === '') continue;

                $answer =  new Answer();
                $answer->questionid = $question->id;
                $answer->answer = $jawaban;
                $answer->is_correct = $label === $request->input('correct');
                $answer->save();
            }
        } else if ($type === 'uraian_singkat') {
            $answer =  new Answer();
            $answer->questionid = $question->id;
            $answer->answer = $request->input('answer');
            $answer->is_correct = true;
            $answer->save();

            Log::info('Jawaban uraian_singkat berhasil disimpan', [
                'question_id' => $question->id,
                'answer_id' => $answer->id,
                'answer_text' => $answer->answer,
            ]);
        } else if ($type === 'esai') {
            $answer =  new Answer();
            $answer->questionid = $question->id;
            $answer->answer = $request->input('answer');
            $answer->is_correct = true;
            $answer->score = $request->input('score');
            $answer->save();            
        }

        return redirect()->route('question.detail', $materialid)
            ->with('success', 'Soal berhasil disimpan.');
    }

    public function editDetailType($questionid)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = Question::with('answers')->findOrFail($questionid);
        $type = $question->type;

        $view = match ($type) {
            'pilihan_ganda' => 'admin.questionbank.question.form.form_pilihan_ganda',
            'uraian_singkat' => 'admin.questionbank.question.form.form_uraian_singkat',
            'esai' => 'admin.questionbank.question.form.form_esai',
        };

        return view($view, [
            'title' => 'Edit Soal ' . ucwords(str_replace('_', ' ', $type)),
            'question' => $question,
            'type' => $type,
            'mode' => 'edit',
        ]);
    }

    public function processEditDetailType(Request $request, $questionid, $type)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = Question::with('answers')->findOrFail($questionid);

        if ($type === 'pilihan_ganda') {
            $request->validate([
                'question' => 'required|string',
                'correct' => 'required|in:A,B,C,D,E',
                'answer_1' => 'required|string',
                'answer_2' => 'required|string',
                'answer_3' => 'required|string',
                'answer_4' => 'required|string',
                'answer_5' => 'required|string',
            ]);
        } else if ($type === 'uraian_singkat') {
            $request->validate([
                'question' => 'required|string',
                'answer' => 'required|string',
            ]);
        } else if ($type === 'esai') {
            $request->validate([
                'question' => 'required|string',
                'answer' => 'required|string',
                'score' => 'required|numeric',
            ]);
        } else {
            abort(400, 'Tipe soal tidak dikenali.');
        }

        // Update soal utama
        $question->question = $request->input('question');
        if ($request->filled('uploaded_images')) {
            $newImages = explode(',', $request->input('uploaded_images'));
            $oldImages = [];

            if (!empty($question->img)) {
                $oldImages = explode(',', $question->img);
                // Hapus yang sudah tidak dipakai
                foreach ($oldImages as $img) {
                    if (!in_array($img, $newImages)) {
                        Storage::disk('public')->delete('uploads/images/question/' . $img);
                    }
                }
            }
            $question->img = implode(',', $newImages);
        } else {
            // Tidak ada gambar sama sekali, hapus semua gambar lama
            if (!empty($question->img)) {
                $oldImages = explode(',', $question->img);
                foreach ($oldImages as $img) {
                    Storage::disk('public')->delete('uploads/images/question/' . $img);
                }
            }
            $question->img = null;
        }

        $question->save();

        if ($type === 'pilihan_ganda') {
            $opsi = [
                'A' => $request->input('answer_1'),
                'B' => $request->input('answer_2'),
                'C' => $request->input('answer_3'),
                'D' => $request->input('answer_4'),
                'E' => $request->input('answer_5'),
            ];

            $answers = $question->answers->values();
            $labels = ['A', 'B', 'C', 'D', 'E'];

            // Update jawaban satu-satu sesuai label
            foreach ($labels as $i => $label) {
                $answerInput = $request->input('answer_' . ($i + 1));
                $isCorrect = $request->input('correct') === $label;

                if (isset($answers[$i])) {
                    $answers[$i]->update([
                        'answer' => $answerInput,
                        'is_correct' => $isCorrect,
                    ]);
                }
            }
        } else if ($type === 'uraian_singkat') {
            $answer = $question->answers->first();
            if ($answer) {
                $answer->update([
                    'answer' => $request->input('answer'),
                    'is_correct' => true,
                ]);
            }
        } else if ($type === 'esai') {
            $answer = $question->answers->first();
            if ($answer) {
                $answer->update([
                    'answer' => $request->input('answer'),
                    'is_correct' => true,
                    'score' => $request->input('score'),
                ]);
            } 
        }

        return redirect()->route('question.detail', $question->questionmaterialid)
            ->with('success', 'Soal berhasil diperbarui!');
    }

    public function deleteDetail($questionid)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = Question::find($questionid);
        $materialid = $question->questionmaterialid;

        if (!empty($question->img)) {
            $images = explode(',', $question->img);
            foreach ($images as $img) {
                if (Storage::disk('public')->exists('uploads/images/question/' . $img)) {
                    Storage::disk('public')->delete('uploads/images/question/' . $img);
                }
            }
        }

        $question->answers()->delete();
        $question->delete();

        return redirect()->route('question.detail', $materialid)
            ->with('success', 'Soal berhasil dihapus.');
    }

    public function getQuestion($id, $type)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $questions = Question::select('question.*', 'answer.*')
            ->join('answer', 'answer.questionid', '=', 'question.id') // Ganti sesuai relasi sebenarnya
            ->where('question.questionmaterialid', $id) // Sesuaikan nama kolomnya
            ->where('question.type', $type)
            ->get();

        if ($type === 'pilihan_ganda') {
            return view('admin.questionbank.question.components.pilihan_ganda', compact('questions'));
        } elseif ($type === 'uraian_singkat') {
            return view('admin.questionbank.question.components.uraian_singkat', compact('questions'));
        } elseif ($type === 'esai') {
            return view('admin.questionbank.question.components.esai', compact('questions'));
        } else {
            abort(404, 'Tipe soal tidak dikenali.');
        }
    }

    public function upload(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $request->validate([
            'file' => 'required|image|mimes:jpeg,jpg,png,webp|max:2048',
        ]);

        $file = $request->file('file');
        $filename = hash('sha256', time() . '_' . uniqid()) . '.' . $file->getClientOriginalExtension();
        $file->storeAs('uploads/images/question/', $filename, 'public');


        return response()->json(['filename' => $filename]);
    }
}
