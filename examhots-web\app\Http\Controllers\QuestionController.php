<?php

namespace App\Http\Controllers;

use App\Models\Answer;
use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\QuestionMaterial;

class QuestionController extends Controller
{
    public function index()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = QuestionMaterial::all();

        return view('admin.questionbank.question.index', [
            'title' => 'Bank Soal',
            'question' => $question,
        ]);
    }

    public function add()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        return view('admin.questionbank.question.add', [
            'title' => 'Tambah Bank Soal',
        ]);
    }

    public function processAdd(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $request->validate([
            'name' => 'required',
            'description' => 'required',
        ]);

        QuestionMaterial::create([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        return redirect()->route('question.index')->with('success', 'Data bank soal berhasil ditambahkan');
    }

    public function edit($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = QuestionMaterial::find($id);

        return view('admin.questionbank.question.edit', [
            'title' => 'Edit Bank Soal',
            'question' => $question,
        ]);
    }

    public function processEdit(Request $request, $id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $request->validate([
            'name' => 'required',
            'description' => 'required',
        ]);

        QuestionMaterial::where('id', $id)->update([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        return redirect()->route('question.index')->with('success', 'Data bank soal berhasil diubah');
    }

    public function delete($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = QuestionMaterial::find($id);

        $question->delete();

        return redirect()->route('question.index')->with('success', 'Data bank soal berhasil dihapus');
    }

    public function detail($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $questionmaterial = QuestionMaterial::find($id);
        $question = Question::join('answer', 'question.id', '=', 'answer.questionid')
            ->where('question.questionmaterialid', $id)
            ->select(             
                'question.question',              
                'answer.answer',
                'answer.is_correct'
            )
            ->get()
            ->groupBy('questionid');
        return view('admin.questionbank.question.detail', [
            'title' => 'Detail Bank Soal',
            'questionmaterial' => $questionmaterial,
            'question' => $question,
        ]);
    }

    public function addDetail(Request $request, $id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $question = QuestionMaterial::findOrFail($id);

        return view('admin.questionbank.question.form.choose', compact('question') + [
            'title' => 'Form Pilih Soal',
        ]);
    }

    public function chooseDetailType(Request $request, $id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $request->validate([
            'type' => 'required|in:pilihan_ganda,uraian_singkat,esai',
        ]);

        $type = $request->input('type');

        return redirect()->route('question.detail.add.type', [
            'id' => $id,
            'type' => $type,
        ]);
    }

    public function addDetailType($id, $type)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $allowedTypes = ['pilihan_ganda', 'uraian_singkat', 'esai'];
        if (!in_array($type, $allowedTypes)) {
            abort(404, 'Tipe soal tidak dikenali');
        }

        $question = QuestionMaterial::findOrFail($id);

        $view = match ($type) {
            'pilihan_ganda' => 'admin.questionbank.question.form.form_pilihan_ganda',
            'uraian_singkat' => 'admin.questionbank.question.form.form_uraian_singkat',
            'esai' => 'admin.questionbank.question.form.form_esai',
        };

        return view($view, [
            'title' => 'Buat Soal ' . ucwords(str_replace('_', ' ', $type)),
            'question' => $question,
            'type' => $type,
        ]);
    }

    public function processAddDetailType(Request $request, $id, $type)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        if ($type === 'pilihan_ganda') {
            $request->validate([
                'question' => 'required|string',
                'answer_1' => 'required|string',
                'answer_2' => 'required|string',
                'answer_3' => 'required|string',
                'answer_4' => 'required|string',
                'answer_5' => 'required|string',
                'correct' => 'required|in:A,B,C,D,E',
            ]);
        } else {
            abort(400, 'Tipe soal tidak dikenali.');
        }

        $question = new Question();
        $question->questionmaterialid = $id;
        $question->question = $request->input('question');
        $question->type = $type;
        $question->save();


        if ($type === 'pilihan_ganda') {
            $opsi = [
                'A' => $request->input('answer_1'),
                'B' => $request->input('answer_2'),
                'C' => $request->input('answer_3'),
                'D' => $request->input('answer_4'),
                'E' => $request->input('answer_5'),
            ];
           
            foreach ($opsi as $label => $jawaban) {
                if (trim($jawaban) === '') continue;

                $answer =  new Answer();
                $answer->questionid = $question->id;
                $answer->answer = $jawaban;
                $answer->is_correct = $label === $request->input('correct');
                $answer->save();
            }
        }

        return redirect()->route('question.detail', $id)
            ->with('success', 'Soal berhasil disimpan.');
    }
}
