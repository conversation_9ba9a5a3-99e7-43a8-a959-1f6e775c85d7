<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'L' => 'DD.MM.YYYY',
    ],
    'months' => ['Qunxa Garablu', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>ah <PERSON>', '<PERSON>asa Dirri', '<PERSON><PERSON>rri', '<PERSON><PERSON><PERSON>', '<PERSON>u', '<PERSON>teli', '<PERSON><PERSON><PERSON>', 'Kaxxa Garablu'],
    'months_short' => ['qun', 'nah', 'cig', 'agd', 'cax', 'qas', 'qad', 'leq', 'way', 'dit', 'xim', 'kax'],
    'weekdays' => ['Acaada', 'Etleeni', '<PERSON><PERSON><PERSON>', 'Arbaqa', 'Kamiisi', 'Gumqata', 'Sabti'],
    'weekdays_short' => ['aca', 'etl', 'tal', 'arb', 'kam', 'gum', 'sab'],
    'weekdays_min' => ['aca', 'etl', 'tal', 'arb', 'kam', 'gum', 'sab'],
    'first_day_of_week' => 6,
    'day_of_first_week_of_year' => 1,
    'meridiem' => ['saaku', 'carra'],

    'year' => ':count gaqambo', // less reliable
    'y' => ':count gaqambo', // less reliable
    'a_year' => ':count gaqambo', // less reliable

    'month' => ':count àlsa',
    'm' => ':count àlsa',
    'a_month' => ':count àlsa',

    'day' => ':count saaku', // less reliable
    'd' => ':count saaku', // less reliable
    'a_day' => ':count saaku', // less reliable

    'hour' => ':count ayti', // less reliable
    'h' => ':count ayti', // less reliable
    'a_hour' => ':count ayti', // less reliable
]);
