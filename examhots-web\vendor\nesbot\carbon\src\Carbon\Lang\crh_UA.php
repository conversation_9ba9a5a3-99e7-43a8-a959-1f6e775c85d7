<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

/*
 * Authors: <AUTHORS>
 */
return array_replace_recursive(require __DIR__.'/en.php', [
    'formats' => [
        'L' => 'DD.MM.YYYY',
    ],
    'months' => ['<PERSON>var', 'Fevral', 'Mart', 'Aprel', 'Mayıs', '<PERSON>yun', '<PERSON>yul', 'Avgust', 'Sentâbr', 'Oktâbr', 'Noyabr', 'Dekabr'],
    'months_short' => ['Yan', 'Fev', 'Mar', 'Apr', 'May', 'İyn', 'İyl', 'Avg', 'Sen', 'Okt', 'Noy', 'Dek'],
    'weekdays' => ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
    'weekdays_short' => ['Baz', 'Ber', '<PERSON>', '<PERSON>ar', 'C<PERSON>', 'Cum', 'Cer'],
    'weekdays_min' => ['Baz', 'Ber', 'Sal', 'Çar', 'Caq', 'Cum', 'Cer'],
    'first_day_of_week' => 1,
    'day_of_first_week_of_year' => 1,
    'meridiem' => ['ÜE', 'ÜS'],

    'year' => ':count yıl',
    'y' => ':count yıl',
    'a_year' => ':count yıl',

    'month' => ':count ay',
    'm' => ':count ay',
    'a_month' => ':count ay',

    'week' => ':count afta',
    'w' => ':count afta',
    'a_week' => ':count afta',

    'day' => ':count kün',
    'd' => ':count kün',
    'a_day' => ':count kün',

    'hour' => ':count saat',
    'h' => ':count saat',
    'a_hour' => ':count saat',

    'minute' => ':count daqqa',
    'min' => ':count daqqa',
    'a_minute' => ':count daqqa',

    'second' => ':count ekinci',
    's' => ':count ekinci',
    'a_second' => ':count ekinci',
]);
