

<?php $__env->startSection('content'); ?>
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Detail Bank Soal</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Ujian</a></li>
                            <li class="breadcrumb-item"><a class="text-muted " href="./index.html">Bank Soal</a></li>
                            <li class="breadcrumb-item" aria-current="page">Detail Bank Soal</li>
                        </ol>
                    </nav>
                </div>

                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h5 class="card-title fw-semibold"><?php echo e($questionmaterial->name); ?></h5>
                            <span class="card-subtitle fw-light"><?php echo e($questionmaterial->description); ?></span>
                        </div>


                        <div class="col-6 text-end gap-3">
                            <a href="<?php echo e(route('question.index')); ?>" class="btn btn-danger">
                                <i class="ti ti-arrow-left"></i> Kembali
                            </a>
                            <a href="<?php echo e(route('question.detail.add', $question->id)); ?>" class="btn btn-primary">
                                <i class="ti ti-plus"></i> Tambah Soal Baru
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <ul class="nav nav-pills mb-3" role="tablist">
                <li class="nav-item">
                    <button type="button" class="nav-link active" id="tab-multiple-choice-tab" data-bs-toggle="tab"
                        data-bs-target="#tab-multiple-choice" aria-selected="true">Pilihan Ganda</button>
                </li>
                <li class="nav-item">
                    <button type="button" class="nav-link" id="tab-short-description-tab" data-bs-toggle="tab"
                        data-bs-target="#tab-short-description" aria-selected="false">Uraian Singkat</button>
                </li>
                <li class="nav-item">
                    <button type="button" class="nav-link" id="tab-essay-tab" data-bs-toggle="tab"
                        data-bs-target="#tab-essay" aria-selected="false">Esai</button>
                </li>
            </ul>


            <div class="tab-content">
                
                <div class="tab-pane fade show active" id="tab-multiple-choice" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between mb-4">
                                <h5>Soal Pilihan Ganda</h5>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead>
                                        <th>No</th>
                                        <th>Soal</th>
                                        <th>Jawaban</th>
                                        <th>Aksi</th>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $q): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><strong><?php echo e($index + 1); ?></strong></td>
                                                <td> <?php echo e($q->question); ?></td>
                                            </tr>
                                            <?php
                                                $labels = ['A', 'B', 'C', 'D', 'E'];
                                            ?>

                                            <?php $__currentLoopData = $q->answer; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $i => $a): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td></td>
                                                    <td class="<?php echo e($a->is_correct ? 'text-danger fw-bold' : ''); ?>">
                                                        <?php echo e($labels[$i] ?? '?'); ?>.
                                                    </td>
                                                    <td class="<?php echo e($a->is_correct ? 'text-danger fw-bold' : ''); ?>">
                                                        <?php echo e($a->answer); ?>

                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                
                <div class="tab-pane fade" id="tab-short-description" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between mb-4">
                                <h5>Soal Uraian Singkat</h5>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead>
                                        <th>No</th>
                                        <th>Soal</th>
                                        <th>Jawaban</th>
                                        <th>Aksi</th>
                                    </thead>
                                    <tbody>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                
                <div class="tab-pane fade" id="tab-essay" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between mb-4">
                                <h5>Soal Essay</h5>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead>
                                        <th>No</th>
                                        <th>Soal</th>
                                        <th>Jawaban</th>
                                        <th>Aksi</th>
                                    </thead>
                                    <tbody>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tabParam = new URLSearchParams(window.location).get('tab');
            const activeTab = tabParam ? '#' + tabParam : localStorage.getItem('activeTab') ||
                '#tab-multiple-choice';

            const activeTabButton = document.querySelector('[data-bs-target="' + activeTab + '"]');
            if (activeTabButton) {
                new bootstrap.Tab(activeTabButton).show();
            }

            document.querySelectorAll('.nav-link').forEach(function(tab) {
                tab.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-bs-target');
                    localStorage.setItem('activeTab', targetTab);
                    history.pushState(null, '', '?tab=' + targetTab.replace('#', ''));

                });
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('main', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\examhots\examhots-web\resources\views/admin/questionbank/question/detail.blade.php ENDPATH**/ ?>